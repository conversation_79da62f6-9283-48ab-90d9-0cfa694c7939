const express = require('express');
const cors = require('cors');
const bonjour = require('bonjour')();
const fs = require('fs');
const path = require('path');
const bodyParser = require('body-parser');
const os = require('os');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const app = express();
const port = 3001;

app.use(cors());
app.use(bodyParser.json());

const configPath = path.join(__dirname, 'config.json');

// Enhanced audio file extensions for cross-platform support
const AUDIO_EXTENSIONS = [
  '.mp3', '.wav', '.ogg', '.flac', '.m4a', '.aac', '.wma', '.opus',
  '.mp4', '.m4p', '.m4b', '.3gp', '.webm', '.oga', '.spx', '.ape',
  '.wv', '.tta', '.tak', '.mpc', '.mp+', '.mpp', '.dts', '.ac3'
];

// Platform-specific default music directories
const getDefaultMusicDirs = () => {
  const platform = os.platform();
  const homeDir = os.homedir();

  switch (platform) {
    case 'win32':
      return [
        path.join(homeDir, 'Music'),
        path.join(homeDir, 'Documents', 'My Music'),
        'C:\\Users\\<USER>\\Music'
      ];
    case 'darwin': // macOS
      return [
        path.join(homeDir, 'Music'),
        path.join(homeDir, 'Music', 'iTunes', 'iTunes Media', 'Music'),
        path.join(homeDir, 'Music', 'Music', 'Media.localized', 'Music')
      ];
    case 'linux':
      return [
        path.join(homeDir, 'Music'),
        path.join(homeDir, 'music'),
        '/usr/share/sounds',
        '/var/lib/mpd/music'
      ];
    default:
      return [path.join(homeDir, 'Music')];
  }
};

// Network discovery storage
let discoveredDevices = [];
let discoveryInProgress = false;

// Read config file
const getConfig = () => {
  const configData = fs.readFileSync(configPath);
  return JSON.parse(configData);
};

// Write config file
const saveConfig = (config) => {
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
};

// Enhanced scan for music files in a directory with metadata
const scanDir = (dir, maxDepth = 10, currentDepth = 0) => {
  let results = [];

  if (currentDepth >= maxDepth) {
    return results;
  }

  try {
    const list = fs.readdirSync(dir);
    list.forEach(file => {
      try {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat && stat.isDirectory()) {
          // Skip hidden directories and system directories
          if (!file.startsWith('.') && !['node_modules', 'System Volume Information'].includes(file)) {
            results = results.concat(scanDir(filePath, maxDepth, currentDepth + 1));
          }
        } else {
          const fileExtension = path.extname(file).toLowerCase();
          if (AUDIO_EXTENSIONS.includes(fileExtension)) {
            results.push({
              path: filePath,
              name: file,
              size: stat.size,
              modified: stat.mtime,
              extension: fileExtension,
              directory: dir
            });
          }
        }
      } catch (error) {
        console.warn(`Error processing file ${file}:`, error.message);
      }
    });
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error.message);
  }

  return results;
};

// Discover network devices using multiple methods
const discoverNetworkDevices = async () => {
  if (discoveryInProgress) {
    return discoveredDevices;
  }

  discoveryInProgress = true;
  discoveredDevices = [];

  try {
    // Method 1: Bonjour/mDNS discovery
    await discoverBonjourDevices();

    // Method 2: UPnP/DLNA discovery
    await discoverUPnPDevices();

    // Method 3: Network scan for common media server ports
    await discoverNetworkShares();

  } catch (error) {
    console.error('Network discovery error:', error);
  } finally {
    discoveryInProgress = false;
  }

  return discoveredDevices;
};

// Bonjour/mDNS device discovery
const discoverBonjourDevices = () => {
  return new Promise((resolve) => {
    const services = ['http', 'smb', 'afp', 'ftp', 'dlna', 'upnp'];
    let completed = 0;

    services.forEach(serviceType => {
      const browser = bonjour.find({ type: serviceType });

      browser.on('up', (service) => {
        const hostAddress = service.host || (service.addresses && service.addresses[0]);
        const device = {
          name: service.name || `${serviceType.toUpperCase()} Device`,
          type: 'bonjour',
          protocol: serviceType,
          host: hostAddress,
          port: service.port,
          url: `${serviceType}://${hostAddress}:${service.port}`,
          txt: service.txt || {},
          discovered: new Date().toISOString()
        };

        // Avoid duplicates
        if (!discoveredDevices.find(d => d.url === device.url)) {
          discoveredDevices.push(device);
          console.log(`Discovered ${serviceType} service:`, device.name);
        }
      });

      // Stop browsing after 5 seconds
      setTimeout(() => {
        browser.stop();
        completed++;
        if (completed === services.length) {
          resolve();
        }
      }, 5000);
    });
  });
};

// UPnP/DLNA device discovery
const discoverUPnPDevices = async () => {
  try {
    // Simple UPnP discovery using SSDP multicast
    const dgram = require('dgram');
    const socket = dgram.createSocket('udp4');

    const ssdpMessage = [
      'M-SEARCH * HTTP/1.1',
      'HOST: ***************:1900',
      'MAN: "ssdp:discover"',
      'ST: upnp:rootdevice',
      'MX: 3',
      '',
      ''
    ].join('\r\n');

    socket.on('message', (msg, rinfo) => {
      const response = msg.toString();
      if (response.includes('SERVER:') && response.includes('LOCATION:')) {
        const locationMatch = response.match(/LOCATION:\s*(.+)/i);
        if (locationMatch) {
          const device = {
            name: 'UPnP Media Server',
            type: 'upnp',
            protocol: 'upnp',
            host: rinfo.address,
            port: rinfo.port,
            url: locationMatch[1].trim(),
            discovered: new Date().toISOString()
          };

          if (!discoveredDevices.find(d => d.host === device.host)) {
            discoveredDevices.push(device);
            console.log('Discovered UPnP device:', device.host);
          }
        }
      }
    });

    socket.bind(() => {
      socket.setBroadcast(true);
      socket.send(ssdpMessage, 1900, '***************');
    });

    // Close socket after 5 seconds
    setTimeout(() => {
      socket.close();
    }, 5000);

  } catch (error) {
    console.error('UPnP discovery error:', error);
  }
};

// Network share and media server discovery
const discoverNetworkShares = async () => {
  const commonPorts = [
    { port: 8080, name: 'HTTP Media Server' },
    { port: 8200, name: 'Jellyfin' },
    { port: 32400, name: 'Plex' },
    { port: 8096, name: 'Emby' },
    { port: 9000, name: 'Subsonic' },
    { port: 4040, name: 'Airsonic' },
    { port: 445, name: 'SMB Share' },
    { port: 139, name: 'NetBIOS' },
    { port: 548, name: 'AFP Share' },
    { port: 21, name: 'FTP Server' }
  ];

  // Get local network range
  const networkInterfaces = os.networkInterfaces();
  const localIPs = [];

  Object.values(networkInterfaces).forEach(interfaces => {
    interfaces.forEach(iface => {
      if (iface.family === 'IPv4' && !iface.internal) {
        localIPs.push(iface.address);
      }
    });
  });

  // Scan common IP ranges for each local network
  for (const localIP of localIPs) {
    const baseIP = localIP.substring(0, localIP.lastIndexOf('.'));

    // Scan a few common IPs (router, NAS devices, etc.)
    const commonIPs = [1, 100, 101, 102, 200, 201, 254];

    for (const lastOctet of commonIPs) {
      const targetIP = `${baseIP}.${lastOctet}`;

      for (const service of commonPorts) {
        try {
          await checkPort(targetIP, service.port, service.name);
        } catch (error) {
          // Ignore connection errors for network scanning
        }
      }
    }
  }
};

// Check if a port is open on a host
const checkPort = (host, port, serviceName) => {
  return new Promise((resolve, reject) => {
    const net = require('net');
    const socket = new net.Socket();

    const timeout = 1000; // 1 second timeout

    socket.setTimeout(timeout);

    socket.on('connect', () => {
      const device = {
        name: serviceName,
        type: 'network',
        protocol: port === 445 || port === 139 ? 'smb' : 'http',
        host: host,
        port: port,
        url: port === 445 || port === 139 ? `smb://${host}` : `http://${host}:${port}`,
        discovered: new Date().toISOString()
      };

      if (!discoveredDevices.find(d => d.url === device.url)) {
        discoveredDevices.push(device);
        console.log(`Discovered ${serviceName} at ${host}:${port}`);
      }

      socket.destroy();
      resolve();
    });

    socket.on('timeout', () => {
      socket.destroy();
      reject(new Error('Timeout'));
    });

    socket.on('error', () => {
      reject(new Error('Connection failed'));
    });

    socket.connect(port, host);
  });
};

// Auto-discover platform-specific music directories
const discoverLocalMusicDirs = () => {
  const defaultDirs = getDefaultMusicDirs();
  const existingDirs = [];

  defaultDirs.forEach(dir => {
    try {
      if (fs.existsSync(dir)) {
        const stats = fs.statSync(dir);
        if (stats.isDirectory()) {
          existingDirs.push({
            path: dir,
            type: 'local',
            platform: os.platform(),
            discovered: true,
            fileCount: 0 // Will be populated when scanned
          });
        }
      }
    } catch (error) {
      console.warn(`Cannot access directory ${dir}:`, error.message);
    }
  });

  return existingDirs;
};

// API to get the list of music files
app.get('/api/music', (req, res) => {
  const config = getConfig();
  let allMusicFiles = [];
  config.musicDirs.forEach(dir => {
    try {
      if (fs.existsSync(dir)) {
        allMusicFiles = allMusicFiles.concat(scanDir(dir));
      }
    } catch (error) {
      console.error(`Error scanning directory ${dir}:`, error);
    }
  });
  res.json(allMusicFiles);
});

// API to serve a music file
app.get('/api/music-file', (req, res) => {
  const filePath = req.query.path;
  if (fs.existsSync(filePath)) {
    res.sendFile(filePath);
  } else {
    res.status(404).send('File not found');
  }
});

// API to get the config
app.get('/api/config', (req, res) => {
  const config = getConfig();
  res.json(config);
});

// API to update the config
app.post('/api/config', (req, res) => {
  const newConfig = req.body;
  saveConfig(newConfig);
  res.json({ message: 'Configuration saved successfully' });
});

// API to discover network devices
app.get('/api/discover-network', async (req, res) => {
  try {
    const devices = await discoverNetworkDevices();
    res.json(devices);
  } catch (error) {
    console.error('Network discovery error:', error);
    res.status(500).json({ error: 'Network discovery failed', details: error.message });
  }
});

// API to discover local music directories
app.get('/api/discover-local', (req, res) => {
  try {
    const localDirs = discoverLocalMusicDirs();
    res.json(localDirs);
  } catch (error) {
    console.error('Local discovery error:', error);
    res.status(500).json({ error: 'Local discovery failed', details: error.message });
  }
});

// API to get platform information
app.get('/api/platform', (req, res) => {
  const platformInfo = {
    platform: os.platform(),
    arch: os.arch(),
    release: os.release(),
    hostname: os.hostname(),
    homedir: os.homedir(),
    tmpdir: os.tmpdir(),
    networkInterfaces: os.networkInterfaces(),
    defaultMusicDirs: getDefaultMusicDirs(),
    supportedFormats: AUDIO_EXTENSIONS
  };

  res.json(platformInfo);
});

// API to scan a specific directory
app.post('/api/scan-directory', (req, res) => {
  const { directory, maxDepth = 5 } = req.body;

  if (!directory) {
    return res.status(400).json({ error: 'Directory path is required' });
  }

  try {
    if (!fs.existsSync(directory)) {
      return res.status(404).json({ error: 'Directory not found' });
    }

    const files = scanDir(directory, maxDepth);
    res.json({
      directory,
      fileCount: files.length,
      files: files.slice(0, 100), // Limit response size
      totalFiles: files.length,
      scannedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Directory scan error:', error);
    res.status(500).json({ error: 'Directory scan failed', details: error.message });
  }
});

// API to test network connection to a device
app.post('/api/test-connection', async (req, res) => {
  const { host, port, protocol } = req.body;

  if (!host) {
    return res.status(400).json({ error: 'Host is required' });
  }

  try {
    await checkPort(host, port || 80, 'Test Connection');
    res.json({
      success: true,
      message: `Successfully connected to ${host}:${port || 80}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.json({
      success: false,
      message: `Failed to connect to ${host}:${port || 80}`,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API to get discovery status
app.get('/api/discovery-status', (req, res) => {
  res.json({
    inProgress: discoveryInProgress,
    devicesFound: discoveredDevices.length,
    lastDiscovery: discoveredDevices.length > 0 ?
      Math.max(...discoveredDevices.map(d => new Date(d.discovered).getTime())) : null
  });
});

// Test POST route
app.post('/api/test', (req, res) => {
  res.json({ message: 'POST route works', body: req.body });
});

// API for filesystem listing (used by directory browser)
app.post('/api/filesystem/list', (req, res) => {
  const { path: dirPath } = req.body;

  if (!dirPath) {
    return res.status(400).json({ error: 'Path is required' });
  }

  try {
    if (!fs.existsSync(dirPath)) {
      return res.status(404).json({ error: 'Directory not found' });
    }

    const stat = fs.statSync(dirPath);
    if (!stat.isDirectory()) {
      return res.status(400).json({ error: 'Path is not a directory' });
    }

    const items = [];
    const files = fs.readdirSync(dirPath);

    files.forEach(file => {
      try {
        const fullPath = path.join(dirPath, file);
        const fileStat = fs.statSync(fullPath);

        // Skip hidden files and system directories
        if (file.startsWith('.')) return;

        const item = {
          name: file,
          path: fullPath,
          type: fileStat.isDirectory() ? 'directory' : 'file',
          size: fileStat.size,
          modified: fileStat.mtime,
          permissions: {
            read: true,
            write: true,
            execute: fileStat.isDirectory()
          }
        };

        // Add extension for files
        if (!fileStat.isDirectory()) {
          item.extension = path.extname(file).toLowerCase();
        }

        items.push(item);
      } catch (error) {
        console.warn(`Error processing file ${file}:`, error.message);
      }
    });

    // Sort: directories first, then files, both alphabetically
    items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1;
      }
      return a.name.localeCompare(b.name);
    });

    res.json({
      path: dirPath,
      items,
      count: items.length
    });

  } catch (error) {
    console.error('Filesystem listing error:', error);
    res.status(500).json({ error: 'Failed to list directory', details: error.message });
  }
});

// API for network discovery (used by network settings)
app.post('/api/network/discover', async (req, res) => {
  const { scanTypes = ['upnp', 'dlna', 'smb', 'nfs'], timeout = 10000 } = req.body;

  try {
    console.log('Starting network discovery with types:', scanTypes);
    const devices = await discoverNetworkDevices();

    // Filter devices by requested scan types
    const filteredDevices = devices.filter(device =>
      scanTypes.includes(device.type) || scanTypes.includes(device.protocol)
    );

    res.json({
      success: true,
      devices: filteredDevices,
      totalFound: filteredDevices.length,
      scanTypes,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Network discovery API error:', error);
    res.status(500).json({
      success: false,
      error: 'Network discovery failed',
      details: error.message,
      devices: []
    });
  }
});

app.listen(port, async () => {
  console.log(`🎵 Wayne's Media Player Helper App`);
  console.log(`📡 Listening at http://localhost:${port}`);
  console.log(`🖥️  Platform: ${os.platform()} ${os.arch()}`);
  console.log(`📁 Default music directories: ${getDefaultMusicDirs().length} found`);

  // Advertise the service using Bonjour
  bonjour.publish({
    name: `Waynes-Media-Helper-${Date.now()}`,
    type: 'http',
    port: port,
    txt: {
      version: '1.0',
      platform: os.platform(),
      features: 'discovery,streaming,metadata'
    }
  });
  console.log('📢 Published service on the network');

  // Start initial network discovery
  console.log('🔍 Starting network discovery...');
  try {
    const devices = await discoverNetworkDevices();
    console.log(`✅ Network discovery complete: ${devices.length} devices found`);

    // Log discovered devices
    devices.forEach(device => {
      console.log(`   📱 ${device.name} (${device.type}) - ${device.url}`);
    });
  } catch (error) {
    console.error('❌ Network discovery failed:', error.message);
  }

  // Discover local music directories
  const localDirs = discoverLocalMusicDirs();
  console.log(`📂 Local music directories discovered: ${localDirs.length}`);
  localDirs.forEach(dir => {
    console.log(`   📁 ${dir.path}`);
  });

  console.log('🚀 Helper app ready for cross-platform media discovery!');
});