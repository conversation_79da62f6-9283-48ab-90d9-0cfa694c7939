/**
 * Mobile-First Responsive Design System
 * Optimized for touch interfaces and mobile devices
 */

// Breakpoints (mobile-first approach)
export const breakpoints = {
  xs: '475px',   // Small phones
  sm: '640px',   // Large phones
  md: '768px',   // Tablets
  lg: '1024px',  // Small laptops
  xl: '1280px',  // Large laptops
  '2xl': '1536px' // Desktops
} as const;

// Spacing system (mobile-optimized)
export const spacing = {
  // Touch-friendly spacing
  touchTarget: '44px', // Minimum touch target size
  touchPadding: '12px', // Padding around touch targets
  
  // Component spacing
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  '2xl': '48px',
  '3xl': '64px',
  
  // Mobile-specific spacing
  mobile: {
    padding: '16px',
    margin: '12px',
    gap: '12px',
    section: '24px'
  },
  
  // Desktop spacing
  desktop: {
    padding: '24px',
    margin: '16px',
    gap: '16px',
    section: '32px'
  }
} as const;

// Typography system (mobile-first)
export const typography = {
  // Font sizes (mobile-first)
  text: {
    xs: 'text-xs',      // 12px
    sm: 'text-sm',      // 14px
    base: 'text-base',  // 16px
    lg: 'text-lg',      // 18px
    xl: 'text-xl',      // 20px
    '2xl': 'text-2xl',  // 24px
    '3xl': 'text-3xl'   // 30px
  },
  
  // Responsive font sizes
  responsive: {
    heading: 'text-xl md:text-2xl lg:text-3xl',
    subheading: 'text-lg md:text-xl',
    body: 'text-sm md:text-base',
    caption: 'text-xs md:text-sm',
    button: 'text-sm md:text-base'
  },
  
  // Font weights
  weight: {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  },
  
  // Line heights (optimized for mobile reading)
  leading: {
    tight: 'leading-tight',
    normal: 'leading-normal',
    relaxed: 'leading-relaxed'
  }
} as const;

// Color system (responsive dark/light theme)
export const colors = {
  // Background colors
  background: {
    primary: 'bg-background',
    secondary: 'bg-secondary',
    tertiary: 'bg-muted',
    overlay: 'bg-black bg-opacity-25 dark:bg-black dark:bg-opacity-50'
  },

  // Text colors
  text: {
    primary: 'text-foreground',
    secondary: 'text-muted-foreground',
    tertiary: 'text-muted-foreground',
    muted: 'text-muted-foreground'
  },

  // Border colors
  border: {
    primary: 'border-border',
    secondary: 'border-border',
    accent: 'border-primary'
  },

  // Interactive colors
  interactive: {
    primary: 'text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300',
    secondary: 'text-gray-600 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300',
    danger: 'text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300',
    success: 'text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300'
  }
} as const;

// Component variants
export const variants = {
  // Button variants
  button: {
    primary: 'bg-primary hover:bg-primary/90 text-primary-foreground focus:ring-primary',
    secondary: 'bg-secondary hover:bg-secondary/80 text-secondary-foreground focus:ring-secondary',
    ghost: 'bg-transparent hover:bg-accent text-foreground hover:text-accent-foreground focus:ring-accent',
    danger: 'bg-destructive hover:bg-destructive/90 text-destructive-foreground focus:ring-destructive'
  },

  // Input variants
  input: {
    default: 'bg-input border-border text-foreground placeholder-muted-foreground focus:ring-ring focus:border-transparent',
    error: 'bg-input border-destructive text-foreground placeholder-muted-foreground focus:ring-destructive focus:border-transparent'
  }
} as const;

// Touch-friendly sizing
export const sizing = {
  // Touch targets (minimum 44px)
  touchTarget: {
    sm: 'min-h-[44px] min-w-[44px]',
    md: 'min-h-[48px] min-w-[48px]',
    lg: 'min-h-[52px] min-w-[52px]'
  },
  
  // Button sizes
  button: {
    sm: 'px-3 py-2 text-sm min-h-[40px]',
    md: 'px-4 py-2.5 text-sm min-h-[44px]',
    lg: 'px-6 py-3 text-base min-h-[48px]'
  },
  
  // Input sizes
  input: {
    sm: 'px-3 py-2 text-sm min-h-[40px]',
    md: 'px-3 py-2.5 text-sm min-h-[44px]',
    lg: 'px-4 py-3 text-base min-h-[48px]'
  }
} as const;

// Animation and transitions
export const animations = {
  // Standard transitions
  transition: 'transition-colors duration-200 ease-in-out',
  transitionAll: 'transition-all duration-200 ease-in-out',
  
  // Focus states
  focus: 'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900',
  
  // Hover states
  hover: 'hover:transition-colors hover:duration-150'
} as const;

// Layout utilities
export const layout = {
  // Container classes
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  
  // Flex utilities
  flex: {
    center: 'flex items-center justify-center',
    between: 'flex items-center justify-between',
    start: 'flex items-center justify-start',
    column: 'flex flex-col',
    wrap: 'flex flex-wrap'
  },
  
  // Grid utilities
  grid: {
    responsive: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6',
    auto: 'grid grid-cols-auto-fit gap-4 md:gap-6'
  },
  
  // Spacing utilities
  space: {
    section: 'space-y-6 md:space-y-8',
    items: 'space-y-3 md:space-y-4',
    inline: 'space-x-2 md:space-x-3'
  }
} as const;

// Utility functions
export const utils = {
  // Combine classes
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(' ');
  },
  
  // Get responsive class
  responsive: (mobile: string, desktop?: string): string => {
    return desktop ? `${mobile} md:${desktop}` : mobile;
  },
  
  // Touch-friendly class builder
  touchFriendly: (baseClasses: string): string => {
    return `${baseClasses} touch-manipulation select-none`;
  }
};

// Component base classes
export const baseClasses = {
  // Card components
  card: 'bg-white border border-gray-200 dark:bg-gray-950 dark:border-gray-900 rounded-lg',

  // Interactive elements
  interactive: 'touch-manipulation focus:outline-none transition-colors duration-200',

  // Form elements
  formElement: 'rounded-md border focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-950',

  // Text elements
  text: {
    heading: 'font-semibold text-gray-900 dark:text-white',
    body: 'text-gray-700 dark:text-gray-300',
    caption: 'text-gray-600 dark:text-gray-400'
  }
} as const;

export type DesignSystem = {
  breakpoints: typeof breakpoints;
  spacing: typeof spacing;
  typography: typeof typography;
  colors: typeof colors;
  variants: typeof variants;
  sizing: typeof sizing;
  animations: typeof animations;
  layout: typeof layout;
  utils: typeof utils;
  baseClasses: typeof baseClasses;
};
