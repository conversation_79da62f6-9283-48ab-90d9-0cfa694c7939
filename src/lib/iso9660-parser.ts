import { KaitaiStream } from 'kaitai-struct/KaitaiStream';

/**
 * ISO9660 filesystem parser implementation
 * Based on the Kaitai Struct format specification
 */
export class ISO9660Parser {
  private stream: KaitaiStream;
  private sectorSize = 2048;

  constructor(buffer: A<PERSON>yBuffer) {
    this.stream = new KaitaiStream(buffer);
  }

  /**
   * Parse the primary volume descriptor
   */
  parsePrimaryVolumeDescriptor() {
    // Seek to sector 16 (0x10) where the primary volume descriptor is located
    this.stream.seek(0x10 * this.sectorSize);
    
    const type = this.stream.readU1();
    const magic = this.stream.readBytes(5);
    const magicStr = String.fromCharCode(...magic);
    
    if (magicStr !== 'CD001') {
      throw new Error('Invalid ISO9660 signature');
    }
    
    const version = this.stream.readU1();
    
    if (type !== 1) {
      throw new Error('Not a primary volume descriptor');
    }
    
    // Skip unused byte
    this.stream.readU1();
    
    // Read system ID (32 bytes)
    const systemId = this.readString(32);
    
    // Read volume ID (32 bytes) - this is the album title
    const volumeId = this.readString(32);
    
    // Skip unused bytes (8)
    this.stream.readBytes(8);
    
    // Read volume space size (both endian)
    const volSpaceSizeLe = this.stream.readU4le();
    const volSpaceSizeBe = this.stream.readU4be();
    
    // Skip unused bytes (32)
    this.stream.readBytes(32);
    
    // Read volume set size
    const volSetSizeLe = this.stream.readU2le();
    const volSetSizeBe = this.stream.readU2be();
    
    // Read volume sequence number
    const volSeqNumLe = this.stream.readU2le();
    const volSeqNumBe = this.stream.readU2be();
    
    // Read logical block size
    const logicalBlockSizeLe = this.stream.readU2le();
    const logicalBlockSizeBe = this.stream.readU2be();
    
    // Read path table size
    const pathTableSizeLe = this.stream.readU4le();
    const pathTableSizeBe = this.stream.readU4be();
    
    // Read path table locations
    const lbaPathTableLe = this.stream.readU4le();
    const lbaOptPathTableLe = this.stream.readU4le();
    const lbaPathTableBe = this.stream.readU4be();
    const lbaOptPathTableBe = this.stream.readU4be();
    
    // Read root directory entry (34 bytes)
    const rootDirEntry = this.parseDirectoryEntry();
    
    // Skip to publisher ID (after vol_set_id)
    this.stream.seek(this.stream.pos + 128); // Skip vol_set_id
    
    // Read publisher ID (128 bytes) - this could be the artist
    const publisherId = this.readString(128);
    
    return {
      systemId: systemId.trim(),
      volumeId: volumeId.trim(),
      publisherId: publisherId.trim(),
      volSpaceSize: volSpaceSizeLe,
      logicalBlockSize: logicalBlockSizeLe,
      pathTableSize: pathTableSizeLe,
      lbaPathTableLe,
      rootDirEntry
    };
  }

  /**
   * Parse a directory entry
   */
  parseDirectoryEntry() {
    const startPos = this.stream.pos;
    const len = this.stream.readU1();
    
    if (len === 0) {
      return null;
    }
    
    const lenExtAttrRec = this.stream.readU1();
    
    // Read LBA extent (both endian)
    const lbaExtentLe = this.stream.readU4le();
    const lbaExtentBe = this.stream.readU4be();
    
    // Read size extent (both endian)
    const sizeExtentLe = this.stream.readU4le();
    const sizeExtentBe = this.stream.readU4be();
    
    // Read datetime (7 bytes)
    const datetime = {
      year: this.stream.readU1(),
      month: this.stream.readU1(),
      day: this.stream.readU1(),
      hour: this.stream.readU1(),
      minute: this.stream.readU1(),
      sec: this.stream.readU1(),
      timezone: this.stream.readU1()
    };
    
    const fileFlags = this.stream.readU1();
    const fileUnitSize = this.stream.readU1();
    const interleaveGapSize = this.stream.readU1();
    
    // Read volume sequence number (both endian)
    const volSeqNumLe = this.stream.readU2le();
    const volSeqNumBe = this.stream.readU2be();
    
    const lenFileName = this.stream.readU1();
    const fileName = this.readString(lenFileName);
    
    // Handle padding
    if (lenFileName % 2 === 0) {
      this.stream.readU1();
    }
    
    // Read any remaining bytes in the entry
    const bytesRead = this.stream.pos - startPos;
    const remainingBytes = len - bytesRead;
    if (remainingBytes > 0) {
      this.stream.readBytes(remainingBytes);
    }
    
    return {
      len,
      lenExtAttrRec,
      lbaExtent: lbaExtentLe,
      sizeExtent: sizeExtentLe,
      datetime,
      fileFlags,
      fileName: fileName.trim(),
      isDirectory: (fileFlags & 2) !== 0
    };
  }

  /**
   * Parse directory entries from a directory
   */
  parseDirectoryEntries(lbaExtent: number, sizeExtent: number) {
    this.stream.seek(lbaExtent * this.sectorSize);
    const endPos = this.stream.pos + sizeExtent;
    const entries = [];
    
    while (this.stream.pos < endPos) {
      const entry = this.parseDirectoryEntry();
      if (!entry) break;
      entries.push(entry);
    }
    
    return entries;
  }

  /**
   * Extract file data
   */
  extractFileData(lbaExtent: number, sizeExtent: number): Uint8Array {
    this.stream.seek(lbaExtent * this.sectorSize);
    return this.stream.readBytes(sizeExtent);
  }

  /**
   * Read a string of specified length
   */
  private readString(length: number): string {
    const bytes = this.stream.readBytes(length);
    return String.fromCharCode(...bytes).replace(/\0/g, '');
  }

  /**
   * Get the current stream position
   */
  get position(): number {
    return this.stream.pos;
  }

  /**
   * Get the total size of the stream
   */
  get size(): number {
    return this.stream.size;
  }
}
