"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import * as mm from 'music-metadata-browser';
import { MediaPlayer } from '@/components/MediaPlayer';
import { Playlist } from '@/components/Playlist';
import { QueueManager } from '@/components/QueueManager';
import { MusicItem } from '@/components/MusicItem';
import { ISOManager } from '@/components/ISOManager';
import { Button } from '@/components/ui/Button';
import { ISOAlbum, ISOTrack } from '@/lib/iso-parser';

export default function Home() {
  const [musicFiles, setMusicFiles] = useState<any[]>([]);
  const [playlist, setPlaylist] = useState<any[]>([]);
  const [queue, setQueue] = useState<any[]>([]);
  const [currentTrackIndex, setCurrentTrackIndex] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState('collection'); // collection, network, isos
  const [musicDirs, setMusicDirs] = useState<any[]>([]);
  const [newDir, setNewDir] = useState('');
  const [helperAppUrl, setHelperAppUrl] = useState('http://localhost:3001'); // Default URL for the helper app
  const [networkSources, setNetworkSources] = useState<any[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('online');
  const [showPlaylist, setShowPlaylist] = useState(false);
  const [showQueue, setShowQueue] = useState(false);
  const [playlists, setPlaylists] = useState<Array<{ id: string; name: string; tracks: any[] }>>([]);
  const [isoAlbums, setIsoAlbums] = useState<ISOAlbum[]>([]);

  const fileInputRef = useRef<any>(null);

  // Detect platform and capabilities
  const detectPlatform = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    const platform = navigator.platform.toLowerCase();

    return {
      isMobile: /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent),
      isIOS: /ipad|iphone|ipod/.test(userAgent),
      isAndroid: /android/.test(userAgent),
      isDesktop: !(/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)),
      isMac: platform.includes('mac'),
      isWindows: platform.includes('win'),
      isLinux: platform.includes('linux'),
      supportsFileAPI: 'File' in window && 'FileReader' in window && 'FileList' in window && 'Blob' in window,
      supportsMediaSession: 'mediaSession' in navigator,
      supportsServiceWorker: 'serviceWorker' in navigator,
      supportsNotifications: 'Notification' in window
    };
  };

  // Enhanced network discovery for cross-platform media sources
  const scanNetworkSources = async () => {
    setIsScanning(true);
    try {
      console.log('Starting network discovery...');

      // Discover network devices
      const networkResponse = await fetch(`${helperAppUrl}/api/discover-network`);
      if (networkResponse.ok) {
        const networkDevices = await networkResponse.json();
        console.log('Network devices found:', networkDevices);

        // Discover local directories
        const localResponse = await fetch(`${helperAppUrl}/api/discover-local`);
        const localDirs = localResponse.ok ? await localResponse.json() : [];
        console.log('Local directories found:', localDirs);

        // Combine network and local sources
        const allSources = [
          ...networkDevices.map((device: any) => ({
            ...device,
            category: 'network',
            status: 'discovered'
          })),
          ...localDirs.map((dir: any) => ({
            name: `Local: ${dir.path}`,
            type: 'local',
            protocol: 'file',
            host: 'localhost',
            url: dir.path,
            category: 'local',
            status: 'available',
            platform: dir.platform,
            discovered: new Date().toISOString()
          }))
        ];

        setNetworkSources(allSources);
        console.log('Total sources discovered:', allSources.length);
      }
    } catch (error) {
      console.error('Network scan failed:', error);

      // Try to get discovered network sources from helper app
      try {
        const discoveryResponse = await fetch(`${helperAppUrl}/api/discover-network`);
        if (discoveryResponse.ok) {
          const discoveredDevices = await discoveryResponse.json();
          const networkSources = discoveredDevices.map((device: any) => ({
            name: device.name,
            url: device.url,
            type: device.type,
            category: 'network',
            host: device.host,
            port: device.port,
            discovered: true
          }));
          setNetworkSources(networkSources);
        } else {
          setNetworkSources([]);
        }
      } catch (fallbackError) {
        console.error('Network discovery failed:', fallbackError);
        setNetworkSources([]);
      }
    }
    setIsScanning(false);
  };

  // Auto-discover sources on component mount
  const autoDiscoverSources = async () => {
    try {
      const statusResponse = await fetch(`${helperAppUrl}/api/discovery-status`);
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        if (!status.inProgress && status.devicesFound === 0) {
          // Only auto-discover if no previous discovery was done
          await scanNetworkSources();
        }
      }
    } catch (error) {
      console.log('Auto-discovery skipped:', error);
    }
  };

  // Check connection status
  const checkConnectionStatus = () => {
    setConnectionStatus(navigator.onLine ? 'online' : 'offline');
  };

  // Test connection to a network source
  const testConnection = async (source: any) => {
    try {
      const response = await fetch(`${helperAppUrl}/api/test-connection`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          host: source.host,
          port: source.port,
          protocol: source.protocol
        })
      });

      const result = await response.json();
      console.log('Connection test result:', result);

      // Update the source status in the UI
      setNetworkSources(prev => prev.map(s =>
        s.url === source.url ? { ...s, status: result.success ? 'connected' : 'failed' } : s
      ));

      return result.success;
    } catch (error) {
      console.error('Connection test failed:', error);
      return false;
    }
  };

  // Add a custom network source
  const addNetworkSource = async (url: any, name: any) => {
    if (!url.trim()) return;

    try {
      // Parse the URL to extract components
      const urlObj = new URL(url);
      const newSource = {
        name: name || `Custom: ${urlObj.hostname}`,
        url: url,
        type: urlObj.protocol.replace(':', ''),
        protocol: urlObj.protocol.replace(':', ''),
        host: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        category: 'network',
        status: 'added',
        discovered: new Date().toISOString()
      };

      setNetworkSources(prev => [...prev, newSource]);

      // Test the connection
      await testConnection(newSource);

    } catch (error) {
      console.error('Invalid URL:', error);
      // Add as a simple entry anyway
      const newSource = {
        name: name || `Custom: ${url}`,
        url: url,
        type: 'custom',
        protocol: 'unknown',
        category: 'network',
        status: 'added',
        discovered: new Date().toISOString()
      };

      setNetworkSources(prev => [...prev, newSource]);
    }
  };

  const fetchConfig = useCallback(async () => {
    try {
      const response = await fetch(`${helperAppUrl}/api/config`);
      const data = await response.json();
      setMusicDirs(data.musicDirs);
    } catch (error) {
      console.error('Error fetching config:', error);
    }
  }, [helperAppUrl]);

  const saveConfig = useCallback(async (newDirs: any) => {
    try {
      await fetch(`${helperAppUrl}/api/config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ musicDirs: newDirs }),
      });
      setMusicDirs(newDirs);
    } catch (error) {
      console.error('Error saving config:', error);
    }
  }, [helperAppUrl]);

  const handleAddDir = () => {
    console.log('handleAddDir called');
    console.log('newDir:', newDir);
    console.log('musicDirs:', musicDirs);
    if (newDir && !musicDirs.includes(newDir)) {
      console.log('Adding new directory:', newDir);
      const newDirs = [...musicDirs, newDir];
      saveConfig(newDirs);
      setNewDir('');
    } else {
      console.log('Condition not met: newDir is empty or already exists.');
    }
  };

  const handleRemoveDir = (dirToRemove: any) => {
    const newDirs = musicDirs.filter(dir => dir !== dirToRemove);
    saveConfig(newDirs);
  };

  const fetchMusicFiles = useCallback(async () => {
    try {
      const response = await fetch(`${helperAppUrl}/api/music`);
      const data = await response.json();
      const newMusicFiles = await Promise.all(data.map(async (file: any) => {
        const fileUrl = `${helperAppUrl}/api/music-file?path=${encodeURIComponent(file.path)}`;
        // We can't get metadata from a remote file directly in the browser
        // The helper app would need to be updated to extract metadata
        return {
          ...file,
          url: fileUrl,
        };
      }));
      setMusicFiles(newMusicFiles);
    } catch (error) {
      console.error('Error fetching music files:', error);
    }
  }, [helperAppUrl]);

  const clearPlaylist = () => {
    setPlaylist([]);
    setCurrentTrackIndex(0);
  };

  const removeFromCurrentPlaylist = (index: number) => {
    setPlaylist(prev => {
      const newPlaylist = prev.filter((_, i) => i !== index);
      if (index === currentTrackIndex && newPlaylist.length > 0) {
        setCurrentTrackIndex(Math.min(currentTrackIndex, newPlaylist.length - 1));
      } else if (index < currentTrackIndex) {
        setCurrentTrackIndex(prev => prev - 1);
      }
      return newPlaylist;
    });
  };

  const playTrack = (track: any) => {
    // Add to playlist if not already there
    const existingIndex = playlist.findIndex(t => t.url === track.url);
    if (existingIndex >= 0) {
      setCurrentTrackIndex(existingIndex);
    } else {
      setPlaylist(prev => [...prev, track]);
      setCurrentTrackIndex(playlist.length);
    }
  };

  const playAll = (tracks: any) => {
    setPlaylist(tracks);
    setCurrentTrackIndex(0);
  };

  const addToQueue = (track: any) => {
    setQueue(prevQueue => [...prevQueue, track]);
  };

  const playNext = (track: any) => {
    const newQueue = [...queue];
    newQueue.splice(currentTrackIndex + 1, 0, track);
    setQueue(newQueue);
  };

  const addToPlaylist = (track: any, playlistId?: string) => {
    if (playlistId) {
      setPlaylists(prevPlaylists =>
        prevPlaylists.map(playlist =>
          playlist.id === playlistId
            ? { ...playlist, tracks: [...playlist.tracks, track] }
            : playlist
        )
      );
    } else {
      // Create a new playlist or add to default
      const defaultPlaylist = playlists.find(p => p.name === 'My Playlist');
      if (defaultPlaylist) {
        addToPlaylist(track, defaultPlaylist.id);
      } else {
        const newPlaylist = {
          id: Date.now().toString(),
          name: 'My Playlist',
          tracks: [track]
        };
        setPlaylists(prev => [...prev, newPlaylist]);
      }
    }
  };

  const removeFromCollection = (track: any) => {
    setMusicFiles(prevFiles => prevFiles.filter(file => file.url !== track.url));
  };

  // Playlist management functions
  const createPlaylist = (name: string) => {
    const newPlaylist = {
      id: Date.now().toString(),
      name,
      tracks: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setPlaylists(prev => [...prev, newPlaylist]);
  };

  const deletePlaylist = (id: string) => {
    setPlaylists(prev => prev.filter(p => p.id !== id));
  };

  const renamePlaylist = (id: string, newName: string) => {
    setPlaylists(prev =>
      prev.map(p =>
        p.id === id
          ? { ...p, name: newName, updatedAt: new Date() }
          : p
      )
    );
  };

  const removeFromPlaylist = (playlistId: string, trackUrl: string) => {
    setPlaylists(prev =>
      prev.map(p =>
        p.id === playlistId
          ? { ...p, tracks: p.tracks.filter(t => t.url !== trackUrl), updatedAt: new Date() }
          : p
      )
    );
  };

  const playPlaylist = (playlist: any) => {
    setPlaylist(playlist.tracks);
    setCurrentTrackIndex(0);
  };

  // Queue management functions
  const removeFromQueue = (index: number) => {
    const newQueue = [...queue];
    newQueue.splice(index, 1);
    setQueue(newQueue);

    // Adjust current track index if necessary
    if (index < currentTrackIndex) {
      setCurrentTrackIndex(prev => prev - 1);
    } else if (index === currentTrackIndex && index >= newQueue.length) {
      setCurrentTrackIndex(Math.max(0, newQueue.length - 1));
    }
  };

  const clearQueue = () => {
    setQueue([]);
    setCurrentTrackIndex(0);
  };

  const reorderQueue = (fromIndex: number, toIndex: number) => {
    const newQueue = [...queue];
    const [movedItem] = newQueue.splice(fromIndex, 1);
    newQueue.splice(toIndex, 0, movedItem);
    setQueue(newQueue);

    // Adjust current track index
    if (fromIndex === currentTrackIndex) {
      setCurrentTrackIndex(toIndex);
    } else if (fromIndex < currentTrackIndex && toIndex >= currentTrackIndex) {
      setCurrentTrackIndex(prev => prev - 1);
    } else if (fromIndex > currentTrackIndex && toIndex <= currentTrackIndex) {
      setCurrentTrackIndex(prev => prev + 1);
    }
  };

  const playFromQueue = (index: number) => {
    setCurrentTrackIndex(index);
    setPlaylist(queue);
  };

  useEffect(() => {
    fetchConfig();
    fetchMusicFiles();
    checkConnectionStatus();
    autoDiscoverSources(); // Auto-discover media sources

    // Listen for online/offline events
    window.addEventListener('online', checkConnectionStatus);
    window.addEventListener('offline', checkConnectionStatus);

    return () => {
      window.removeEventListener('online', checkConnectionStatus);
      window.removeEventListener('offline', checkConnectionStatus);
    };
  }, [fetchConfig, fetchMusicFiles]);




  const handleFileChange = async (event: any) => {
    const files = Array.from(event.target.files);

    const newMusicFiles = await Promise.all(files.map(async (file: any) => {
      const fileUrl = URL.createObjectURL(file);

      try {
        // Extract metadata from the audio file
        const metadata = await mm.parseBlob(file);
        const common = metadata.common;

        return {
          file,
          name: file.name,
          title: common.title || file.name.replace(/\.[^/.]+$/, ""),
          artist: common.artist || 'Unknown Artist',
          album: common.album || 'Unknown Album',
          duration: metadata.format.duration || 0,
          picture: common.picture && common.picture.length > 0 ?
            URL.createObjectURL(new Blob([new Uint8Array(common.picture[0].data)], { type: common.picture[0].format })) : null,
          url: fileUrl,
          type: 'local',
          genre: common.genre ? common.genre.join(', ') : undefined,
          year: common.year,
          track: common.track
        };
      } catch (error) {
        console.warn('Failed to extract metadata from', file.name, error);
        return {
          file,
          name: file.name,
          title: file.name.replace(/\.[^/.]+$/, ""),
          artist: 'Unknown Artist',
          album: 'Unknown Album',
          duration: 0,
          picture: null,
          url: fileUrl,
          type: 'local'
        };
      }
    }));

    setMusicFiles(prevFiles => [...prevFiles, ...newMusicFiles]);
  };

  const handleAddMusicClick = () => {
    fileInputRef.current?.click();
  };

  // Initialize playlist with all music files when they're loaded
  useEffect(() => {
    if (musicFiles.length > 0 && playlist.length === 0) {
      setPlaylist(musicFiles);
    }
  }, [musicFiles, playlist.length]);

  // ISO handlers
  const handleISOTrackPlay = (track: ISOTrack) => {
    // Convert ISO track to regular track format
    const regularTrack = {
      url: track.url,
      name: track.title,
      title: track.title,
      artist: track.artist,
      album: track.album,
      duration: track.duration,
      picture: undefined // ISO tracks don't have individual artwork
    };

    // Add to playlist and play
    setPlaylist([regularTrack]);
    setCurrentTrackIndex(0);
  };

  const handleISOAlbumAdd = (album: ISOAlbum) => {
    // Convert ISO album tracks to regular tracks
    const regularTracks = album.tracks.map(track => ({
      url: track.url,
      name: track.title,
      title: track.title,
      artist: track.artist || album.artist,
      album: track.album || album.title,
      duration: track.duration,
      picture: album.coverArt // Use album artwork for all tracks
    }));

    // Add to music files collection
    setMusicFiles(prev => [...prev, ...regularTracks]);

    // Store the ISO album
    setIsoAlbums(prev => [...prev, album]);
  };

  const renderView = () => {
    switch (currentView) {
      case 'collection':
        return (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-2 sm:gap-3 md:gap-4">
            {musicFiles.map((song, index) => (
              <MusicItem
                key={index}
                track={song}
                index={index}
                onPlay={playTrack}
                onAddToQueue={addToQueue}
                onAddToPlaylist={addToPlaylist}
                onPlayNext={playNext}
                onRemove={removeFromCollection}
                playlists={playlists}
              />
            ))}
          </div>
        );
      case 'network':
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <h2 className="text-2xl font-semibold mb-4">Network Discovery</h2>
              <p className="text-muted-foreground">This feature is under development.</p>
              <p className="text-muted-foreground">It will allow you to discover and play music from other devices on your local network.</p>
            </div>
          </div>
        );
      case 'isos':
        return (
          <ISOManager
            onTrackPlay={handleISOTrackPlay}
            onAlbumAdd={handleISOAlbumAdd}
          />
        );
      case 'network':
        return (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-3xl font-semibold">Network Sources</h2>
              <button
                onClick={scanNetworkSources}
                disabled={isScanning}
                className="bg-primary text-primary-foreground font-semibold py-2 px-4 rounded hover:bg-primary/90 disabled:opacity-50"
              >
                {isScanning ? 'Scanning...' : 'Scan Network'}
              </button>
            </div>

            <div className="mb-6">
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                connectionStatus === 'online' ? 'bg-green-600' : 'bg-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  connectionStatus === 'online' ? 'bg-green-300' : 'bg-red-300'
                }`}></div>
                {connectionStatus === 'online' ? 'Online' : 'Offline'}
              </div>
            </div>

            <div className="space-y-6">
              {networkSources.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <p>No media sources found.</p>
                  <p className="text-sm mt-2">Click "Scan Network" to discover local and network media sources.</p>
                </div>
              ) : (
                <>
                  {/* Local Sources */}
                  {networkSources.filter(s => s.category === 'local').length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-primary">📁 Local Sources</h3>
                      <div className="grid gap-3">
                        {networkSources.filter(s => s.category === 'local').map((source, index) => (
                          <div key={`local-${index}`} className="bg-secondary p-4 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-semibold">{source.name}</h4>
                                <p className="text-muted-foreground text-sm">{source.url}</p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span className="inline-block px-2 py-1 rounded text-xs bg-green-600">
                                    LOCAL
                                  </span>
                                  {source.platform && (
                                    <span className="inline-block px-2 py-1 rounded text-xs bg-muted">
                                      {source.platform.toUpperCase()}
                                    </span>
                                  )}
                                </div>
                              </div>
                              <button
                                onClick={() => {
                                  // Scan local directory
                                  fetch(`${helperAppUrl}/api/scan-directory`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ directory: source.url, maxDepth: 3 })
                                  })
                                  .then(res => res.json())
                                  .then(data => {
                                    console.log('Directory scan result:', data);
                                    alert(`Found ${data.fileCount} audio files in ${source.url}`);
                                  })
                                  .catch(err => {
                                    console.error('Scan failed:', err);
                                    alert('Failed to scan directory');
                                  });
                                }}
                                className="bg-primary text-primary-foreground px-3 py-1 rounded text-sm hover:bg-primary/90"
                              >
                                Scan
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Network Sources */}
                  {networkSources.filter(s => s.category === 'network').length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-blue-500">🌐 Network Sources</h3>
                      <div className="grid gap-3">
                        {networkSources.filter(s => s.category === 'network').map((source, index) => (
                          <div key={`network-${index}`} className="bg-secondary p-4 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-semibold">{source.name}</h4>
                                <p className="text-muted-foreground text-sm">{source.url}</p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span className={`inline-block px-2 py-1 rounded text-xs ${
                                    source.type === 'dlna' || source.type === 'upnp' ? 'bg-blue-600' :
                                    source.type === 'http' ? 'bg-green-600' :
                                    source.type === 'smb' ? 'bg-purple-600' :
                                    source.type === 'bonjour' ? 'bg-orange-600' :
                                    source.type === 'plex' ? 'bg-yellow-600' :
                                    source.type === 'jellyfin' ? 'bg-indigo-600' : 'bg-muted'
                                  }`}>
                                    {source.protocol ? source.protocol.toUpperCase() : source.type.toUpperCase()}
                                  </span>
                                  {source.host && (
                                    <span className="text-xs text-muted-foreground">{source.host}:{source.port}</span>
                                  )}
                                  {source.discovered && (
                                    <span className="text-xs text-green-400">✓ Discovered</span>
                                  )}
                                </div>
                              </div>
                              <button
                                onClick={() => testConnection(source)}
                                className={`px-3 py-1 rounded text-sm ${
                                  source.status === 'connected' ? 'bg-green-600 hover:bg-green-700' :
                                  source.status === 'failed' ? 'bg-red-600 hover:bg-red-700' :
                                  'bg-primary hover:bg-primary/90'
                                } text-primary-foreground`}
                              >
                                {source.status === 'connected' ? 'Connected' :
                                 source.status === 'failed' ? 'Failed' : 'Connect'}
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Add Network Source</h3>
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Enter network URL (http://, smb://, ftp://, etc.)"
                    className="flex-1 p-2 bg-input rounded"
                    value={newDir}
                    onChange={(e) => setNewDir(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addNetworkSource(newDir, newDir);
                        setNewDir('');
                      }
                    }}
                  />
                  <button
                    onClick={() => {
                      addNetworkSource(newDir, newDir);
                      setNewDir('');
                    }}
                    className="bg-primary text-primary-foreground font-semibold py-2 px-4 rounded hover:bg-primary/90"
                  >
                    Add Source
                  </button>
                </div>
                <div className="text-sm text-muted-foreground">
                  <p>Examples:</p>
                  <ul className="list-disc list-inside mt-1 space-y-1">
                    <li>http://192.168.1.100:8080/music</li>
                    <li>smb://nas.local/music</li>
                    <li>ftp://mediaserver.local/audio</li>
                    <li>http://plex.local:32400</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Desktop Sidebar */}
      <aside className="hidden md:flex sidebar border-r p-6 flex-col justify-between w-64">
        <div>
          <div className="mb-8">
            <h1 className="text-2xl font-semibold">Wayne's Media</h1>
          </div>
          <nav className="space-y-2">
            <Button
              variant="ghost"
              className={`w-full justify-start sidebar-item ${currentView === 'collection' ? 'active' : ''}`}
              onClick={() => setCurrentView('collection')}
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2H5a2 2 0 00-2 2v2m14 0h-2M5 11H3" />
              </svg>
              My Collection
            </Button>
            <Button
              variant="ghost"
              className={`w-full justify-start sidebar-item ${currentView === 'network' ? 'active' : ''}`}
              onClick={() => setCurrentView('network')}
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              Network
            </Button>
            <Button
              variant="ghost"
              className={`w-full justify-start sidebar-item ${currentView === 'isos' ? 'active' : ''}`}
              onClick={() => setCurrentView('isos')}
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              ISOs
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start sidebar-item"
              onClick={() => window.location.href = '/settings'}
            >
              <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              </svg>
              Settings
            </Button>
          </nav>
        </div>
        <div>
          <input
            type="file"
            multiple
            accept="audio/*,.iso"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
          <Button
            onClick={handleAddMusicClick}
            className="w-full"
          >
            Add Music or ISO
          </Button>
        </div>
      </aside>

      <div className="flex-1 flex flex-col">
        {/* Mobile Header */}
        <header className="bg-background border-b border-border px-4 py-3 md:hidden">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold">Wayne's Media Player</h1>
            <div className="flex items-center space-x-2">
              <Button variant="ghost" size="icon" onClick={() => setShowQueue(!showQueue)}>
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </Button>
              <Button variant="ghost" size="icon" onClick={() => setShowPlaylist(!showPlaylist)}>
                <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden">
          <main className="flex-1 p-3 md:p-8 overflow-y-auto bg-background">
            <div className="max-w-7xl mx-auto">
              {renderView()}
            </div>
          </main>

          {/* Queue Sidebar */}
          {showQueue && (
            <aside className="w-80 bg-card border-l border-border overflow-hidden">
              <QueueManager
                queue={queue}
                currentTrackIndex={currentTrackIndex}
                onPlayTrack={(index) => {
                  // Move track from queue to current playlist and play
                  const track = queue[index];
                  if (track) {
                    setPlaylist([track]);
                    setCurrentTrackIndex(0);
                    // Remove from queue
                    setQueue(prev => prev.filter((_, i) => i !== index));
                  }
                }}
                onRemoveFromQueue={(index) => {
                  setQueue(prev => prev.filter((_, i) => i !== index));
                }}
                onClearQueue={() => setQueue([])}
                onReorderQueue={(fromIndex, toIndex) => {
                  setQueue(prev => {
                    const newQueue = [...prev];
                    const [removed] = newQueue.splice(fromIndex, 1);
                    newQueue.splice(toIndex, 0, removed);
                    return newQueue;
                  });
                }}
                className="h-full"
              />
            </aside>
          )}

          {/* Playlist Sidebar */}
          {showPlaylist && (
            <aside className="w-80 bg-card border-l border-border overflow-hidden">
              <Playlist
                tracks={playlist}
                currentTrackIndex={currentTrackIndex}
                onTrackSelect={setCurrentTrackIndex}
                onTrackRemove={removeFromCurrentPlaylist}
                onPlaylistClear={clearPlaylist}
                className="h-full"
              />
            </aside>
          )}
        </div>

        {/* Media Player */}
        <MediaPlayer
          playlist={playlist}
          currentTrackIndex={currentTrackIndex}
          onTrackChange={setCurrentTrackIndex}
          onPlaylistEnd={() => console.log('Playlist ended')}
          queue={queue}
          onShowQueue={() => {
            setShowQueue(!showQueue);
            if (!showQueue) setShowPlaylist(false); // Close playlist when opening queue
          }}
          onShowPlaylist={() => {
            setShowPlaylist(!showPlaylist);
            if (!showPlaylist) setShowQueue(false); // Close queue when opening playlist
          }}
        />
      </div>
    </div>
  );
}