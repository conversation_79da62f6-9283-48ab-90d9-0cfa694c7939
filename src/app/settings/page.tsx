'use client';

import { useState, useEffect } from 'react';
import { SettingsProvider } from '@/components/settings/SettingsContext';
import { SettingsLayout } from '@/components/settings/SettingsLayout';
import { HelperAppSettings } from '@/components/settings/HelperAppSettings';
import { MusicDirectorySettings } from '@/components/settings/MusicDirectorySettings';
import { NetworkSettings } from '@/components/settings/NetworkSettings';
import { AppearanceSettings } from '@/components/settings/AppearanceSettings';
import { MediaPlayerSettings } from '@/components/settings/MediaPlayerSettings';
import { ExportImportSettings } from '@/components/settings/ExportImportSettings';
import { KeyboardShortcutsSettings } from '@/components/settings/KeyboardShortcutsSettings';

export default function SettingsPage() {
  return (
    <SettingsProvider>
      <SettingsLayout>
        <div className="space-y-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">Settings</h1>
            <p className="text-muted-foreground">Configure your media player preferences</p>
          </div>
          
          <HelperAppSettings />
          <MusicDirectorySettings />
          <NetworkSettings />
          <AppearanceSettings />
          <MediaPlayerSettings />
          <KeyboardShortcutsSettings />
          <ExportImportSettings />
        </div>
      </SettingsLayout>
    </SettingsProvider>
  );
}
