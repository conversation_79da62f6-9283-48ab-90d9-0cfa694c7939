@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme - Teal primary with pink accents */
    --background: 60 50% 96%; /* Very light yellow-green base */
    --foreground: 180 15% 25%; /* Dark teal-gray for text */
    --card: 0 0% 100%; /* Pure white cards */
    --card-foreground: 180 15% 25%;
    --popover: 0 0% 100%;
    --popover-foreground: 180 15% 25%;
    --primary: 180 35% 62%; /* Teal #73C7C7 */
    --primary-foreground: 0 0% 100%;
    --secondary: 165 75% 88%; /* Light teal #A6F1E0 for secondary */
    --secondary-foreground: 180 15% 25%;
    --muted: 60 50% 92%; /* Light yellow #F4F8D3 */
    --muted-foreground: 180 15% 45%;
    --accent: 330 45% 85%; /* Light pink #F7CFD8 for accents */
    --accent-foreground: 180 15% 25%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 180 20% 85%;
    --input: 60 50% 94%;
    --ring: 180 35% 62%;
    --radius: 0.5rem;

    /* Sidebar specific colors */
    --sidebar-background: 165 75% 90%; /* Light teal for sidebar */
    --sidebar-foreground: 180 15% 25%;
    --sidebar-border: 180 20% 80%;
    --sidebar-hover: 330 45% 88%; /* Pink for hover */
    --sidebar-active: 180 35% 62%; /* Teal for active */
    --sidebar-active-foreground: 0 0% 100%;
  }

  .dark {
    /* Dark theme - Sophisticated dark palette */
    --background: 60 6% 12%; /* Black #1E201E */
    --foreground: 30 15% 85%; /* Beige #ECDFCC */
    --card: 60 4% 23%; /* Dark Gray #3C3D37 */
    --card-foreground: 30 15% 85%;
    --popover: 60 4% 23%;
    --popover-foreground: 30 15% 85%;
    --primary: 90 12% 45%; /* Sage #697565 */
    --primary-foreground: 30 15% 85%;
    --secondary: 60 4% 23%; /* Dark Gray #3C3D37 */
    --secondary-foreground: 30 15% 85%;
    --muted: 60 6% 18%; /* Darker variation */
    --muted-foreground: 30 10% 65%;
    --accent: 30 15% 80%; /* Light Beige accent */
    --accent-foreground: 60 6% 12%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 60 4% 30%;
    --input: 60 4% 20%;
    --ring: 90 12% 45%;

    /* Sidebar specific colors for dark mode */
    --sidebar-background: 60 6% 10%; /* Darker black */
    --sidebar-foreground: 30 15% 80%;
    --sidebar-border: 60 4% 25%;
    --sidebar-hover: 60 4% 18%;
    --sidebar-active: 90 12% 45%;
    --sidebar-active-foreground: 30 15% 85%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-inter), Arial, Helvetica, sans-serif;
  }
}

@layer components {
  /* Sidebar theming */
  .sidebar {
    background-color: hsl(var(--sidebar-background));
    color: hsl(var(--sidebar-foreground));
    border-color: hsl(var(--sidebar-border));
  }

  .sidebar-item {
    color: hsl(var(--sidebar-foreground));
    transition: all 0.2s ease-in-out;
  }

  .sidebar-item:hover {
    background-color: hsl(var(--sidebar-hover));
    color: hsl(var(--sidebar-foreground));
  }

  .sidebar-item.active {
    background-color: hsl(var(--sidebar-active));
    color: hsl(var(--sidebar-active-foreground));
  }

  /* Button variants with proper contrast */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border-color: hsl(var(--primary));
  }

  .btn-primary:hover {
    background-color: hsl(var(--primary) / 0.9);
  }

  .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border-color: hsl(var(--border));
  }

  .btn-secondary:hover {
    background-color: hsl(var(--secondary) / 0.8);
  }

  /* Input styling */
  .input-themed {
    background-color: hsl(var(--input));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
  }

  .input-themed:focus {
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 2px hsl(var(--ring) / 0.2);
  }
}
