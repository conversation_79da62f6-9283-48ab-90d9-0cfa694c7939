declare module 'kaitai-struct/KaitaiStream' {
  export class KaitaiStream {
    constructor(buffer: ArrayBuffer | Uint8Array, offset?: number);
    
    // Position and size
    pos: number;
    size: number;
    
    // Seeking
    seek(pos: number): void;
    
    // Reading methods
    readU1(): number;
    readU2le(): number;
    readU2be(): number;
    readU4le(): number;
    readU4be(): number;
    readS1(): number;
    readS2le(): number;
    readS2be(): number;
    readS4le(): number;
    readS4be(): number;
    readF4le(): number;
    readF4be(): number;
    readF8le(): number;
    readF8be(): number;
    
    // Reading bytes
    readBytes(len: number): Uint8Array;
    readBytesFull(): Uint8Array;
    readBytesTerminated(term: number, include: boolean, consume: boolean, eosError: boolean): Uint8Array;
    
    // String reading
    readStrByteLimit(len: number, encoding: string): string;
    readStrEos(encoding: string): string;
    readStrz(encoding: string, term: number, include: boolean, consume: boolean, eosError: boolean): string;
    
    // Utilities
    isEof(): boolean;
    ensureFixedContents(expected: Uint8Array): Uint8Array;
    
    // Static utilities
    static bytesToStr(bytes: Uint8Array, encoding: string): string;
    static byteArrayCompare(a: Uint8Array, b: Uint8Array): number;
    static mod(a: number, b: number): number;
  }
}

declare module 'kaitai-struct' {
  export * from 'kaitai-struct/KaitaiStream';
}
