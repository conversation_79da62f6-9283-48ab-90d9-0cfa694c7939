'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Equalizer } from './Equalizer';
import { useSettings } from './settings/SettingsContext';

interface Track {
  url: string;
  name: string;
  artist?: string;
  album?: string;
  duration?: number;
  picture?: string;
}

interface MediaPlayerProps {
  playlist: Track[];
  currentTrackIndex: number;
  onTrackChange: (index: number) => void;
  onPlaylistEnd?: () => void;
  queue?: Track[];
  onShowQueue?: () => void;
  onShowPlaylist?: () => void;
  showQueueButton?: boolean;
  showPlaylistButton?: boolean;
}

export function MediaPlayer({
  playlist,
  currentTrackIndex,
  onTrackChange,
  onPlaylistEnd,
  queue = [],
  onShowQueue,
  onShowPlaylist,
  showQueueButton = true,
  showPlaylistButton = true
}: MediaPlayerProps) {
  const { state, updateMediaPlayerSettings } = useSettings();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isShuffled, setIsShuffled] = useState(false);
  const [repeatMode, setRepeatMode] = useState<'none' | 'one' | 'all'>('none');
  const [showEqualizer, setShowEqualizer] = useState(false);

  const audioRef = useRef<HTMLAudioElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  const currentTrack = playlist[currentTrackIndex];

  // Load track
  const loadTrack = useCallback((track: Track) => {
    if (!audioRef.current || !track) return;

    console.log('Loading track in MediaPlayer:', track);
    console.log('Track URL:', track.url);
    console.log('Track type:', (track as any).type);

    setIsLoading(true);
    setError(null);
    audioRef.current.src = track.url;
    console.log('Audio element src set to:', audioRef.current.src);
    audioRef.current.load();
    console.log('Audio load() called');
  }, []);

  // Play/Pause
  const togglePlayPause = useCallback(async () => {
    if (!audioRef.current || !currentTrack) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        await audioRef.current.play();
      }
    } catch (error) {
      setError('Failed to play audio');
      console.error('Playback error:', error);
    }
  }, [isPlaying, currentTrack]);

  // Next track
  const nextTrack = useCallback(() => {
    if (playlist.length === 0) return;

    let nextIndex;
    if (isShuffled) {
      nextIndex = Math.floor(Math.random() * playlist.length);
    } else {
      nextIndex = currentTrackIndex + 1;
      if (nextIndex >= playlist.length) {
        if (repeatMode === 'all') {
          nextIndex = 0;
        } else {
          onPlaylistEnd?.();
          return;
        }
      }
    }
    onTrackChange(nextIndex);
  }, [currentTrackIndex, playlist.length, isShuffled, repeatMode, onTrackChange, onPlaylistEnd]);

  // Previous track
  const previousTrack = useCallback(() => {
    if (playlist.length === 0) return;

    let prevIndex;
    if (isShuffled) {
      prevIndex = Math.floor(Math.random() * playlist.length);
    } else {
      prevIndex = currentTrackIndex - 1;
      if (prevIndex < 0) {
        prevIndex = repeatMode === 'all' ? playlist.length - 1 : 0;
      }
    }
    onTrackChange(prevIndex);
  }, [currentTrackIndex, playlist.length, isShuffled, repeatMode, onTrackChange]);

  // Seek (with touch support)
  const handleSeek = useCallback((e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>) => {
    if (!audioRef.current || !progressRef.current) return;

    const rect = progressRef.current.getBoundingClientRect();
    let clientX: number;

    if ('touches' in e) {
      // Touch event
      clientX = e.touches[0]?.clientX || e.changedTouches[0]?.clientX || 0;
    } else {
      // Mouse event
      clientX = e.clientX;
    }

    const percent = (clientX - rect.left) / rect.width;
    const newTime = Math.max(0, Math.min(duration, percent * duration));
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [duration]);

  // Volume control
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!audioRef.current) return;
    
    const clampedVolume = Math.max(0, Math.min(1, newVolume));
    setVolume(clampedVolume);
    audioRef.current.volume = clampedVolume;
    
    if (clampedVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  }, [isMuted]);

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!audioRef.current) return;

    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  }, [isMuted, volume]);

  // Audio event handlers
  const handleLoadStart = () => setIsLoading(true);
  const handleCanPlay = () => setIsLoading(false);
  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);
  const handleEnded = () => {
    if (repeatMode === 'one') {
      audioRef.current?.play();
    } else {
      nextTrack();
    }
  };
  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };
  const handleDurationChange = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration || 0);
    }
  };
  const handleError = (event: any) => {
    console.error('Audio error event:', event);
    console.error('Audio element error:', audioRef.current?.error);
    console.error('Current track:', currentTrack);
    console.error('Audio src:', audioRef.current?.src);
    setError('Failed to load audio');
    setIsLoading(false);
  };

  // Load track when currentTrack changes
  useEffect(() => {
    if (currentTrack) {
      loadTrack(currentTrack);
    }
  }, [currentTrack, loadTrack]);

  // Setup audio event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('durationchange', handleDurationChange);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('durationchange', handleDurationChange);
      audio.removeEventListener('error', handleError);
    };
  }, [nextTrack]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement) return;

      switch (e.code) {
        case 'Space':
          e.preventDefault();
          togglePlayPause();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          previousTrack();
          break;
        case 'ArrowRight':
          e.preventDefault();
          nextTrack();
          break;
        case 'ArrowUp':
          e.preventDefault();
          handleVolumeChange(volume + 0.1);
          break;
        case 'ArrowDown':
          e.preventDefault();
          handleVolumeChange(volume - 0.1);
          break;
        case 'KeyM':
          e.preventDefault();
          toggleMute();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [togglePlayPause, previousTrack, nextTrack, handleVolumeChange, volume, toggleMute]);

  // Format time
  const formatTime = (time: number) => {
    if (!isFinite(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!currentTrack) {
    return (
      <div className="bg-card p-4 text-center text-muted-foreground">
        No track selected
      </div>
    );
  }

  return (
    <div className="bg-card border-t border-border">
      {/* Error Display */}
      {error && (
        <div className="bg-red-900 text-red-200 p-2 text-sm text-center">
          {error}
          <button 
            onClick={() => setError(null)}
            className="ml-2 text-red-400 hover:text-red-300"
          >
            ✕
          </button>
        </div>
      )}

      <div className="p-4">
        {/* Track Info */}
        <div className="flex items-center mb-4">
          <div className="w-12 h-12 bg-secondary rounded-md mr-4 flex-shrink-0">
            {currentTrack.picture ? (
              <img
                src={currentTrack.picture}
                alt={currentTrack.name}
                className="w-full h-full object-cover rounded-md"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                ♪
              </div>
            )}
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-card-foreground truncate">{currentTrack.name}</h3>
            <p className="text-sm text-muted-foreground truncate">{currentTrack.artist || 'Unknown Artist'}</p>
            {currentTrack.album && (
              <p className="text-xs text-muted-foreground truncate">{currentTrack.album}</p>
            )}
          </div>
          {isLoading && (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-400"></div>
          )}
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div
            ref={progressRef}
            className="h-3 md:h-2 bg-secondary rounded-full cursor-pointer touch-manipulation"
            onClick={handleSeek}
            onTouchStart={handleSeek}
            onTouchMove={handleSeek}
          >
            <div
              className="h-full bg-primary rounded-full transition-all duration-100"
              style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-muted-foreground mt-1">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          {/* Left controls */}
          <div className="flex items-center space-x-1 md:space-x-2">
            <button
              onClick={() => setIsShuffled(!isShuffled)}
              className={`p-2 md:p-2 rounded touch-manipulation ${isShuffled ? 'text-primary' : 'text-muted-foreground hover:text-card-foreground'}`}
              title="Shuffle"
            >
              <svg className="w-5 h-5 md:w-4 md:h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M6 2l3 3-3 3V6H3a1 1 0 000 2h3v2L3 7l3-3V2zm8 0v2h3a1 1 0 010 2h-3v2l3-3-3-3V2z"/>
              </svg>
            </button>
          </div>

          {/* Center controls */}
          <div className="flex items-center space-x-2 md:space-x-4">
            <button
              onClick={previousTrack}
              className="text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation"
              title="Previous"
            >
              <svg className="w-7 h-7 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z"/>
              </svg>
            </button>

            <button
              onClick={togglePlayPause}
              disabled={isLoading}
              className="bg-teal-500 hover:bg-teal-600 text-white p-4 md:p-3 rounded-full disabled:opacity-50 touch-manipulation"
              title={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? (
                <svg className="w-8 h-8 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M5 4a1 1 0 011 1v10a1 1 0 01-2 0V5a1 1 0 011-1zM14 4a1 1 0 011 1v10a1 1 0 01-2 0V5a1 1 0 011-1z"/>
                </svg>
              ) : (
                <svg className="w-8 h-8 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M8 5v10l8-5-8-5z"/>
                </svg>
              )}
            </button>

            <button
              onClick={nextTrack}
              className="text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation"
              title="Next"
            >
              <svg className="w-7 h-7 md:w-6 md:h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798L4.555 5.168z"/>
              </svg>
            </button>
          </div>

          {/* Right controls */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all'];
                const currentIndex = modes.indexOf(repeatMode);
                const nextMode = modes[(currentIndex + 1) % modes.length];
                setRepeatMode(nextMode);
              }}
              className={`p-2 md:p-2 rounded touch-manipulation ${repeatMode !== 'none' ? 'text-primary' : 'text-muted-foreground hover:text-card-foreground'}`}
              title={`Repeat: ${repeatMode}`}
            >
              <svg className="w-5 h-5 md:w-4 md:h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"/>
              </svg>
              {repeatMode === 'one' && <span className="text-xs">1</span>}
            </button>

            <div className="flex items-center space-x-1">
              <button
                onClick={toggleMute}
                className="text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation"
                title={isMuted ? 'Unmute' : 'Mute'}
              >
                <svg className="w-5 h-5 md:w-4 md:h-4" fill="currentColor" viewBox="0 0 20 20">
                  {isMuted || volume === 0 ? (
                    <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z"/>
                  ) : (
                    <path d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.828 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.828l3.555-3.793A1 1 0 019.383 3.076zM12 8a1 1 0 011.414 0L15 9.586l1.586-1.586a1 1 0 011.414 1.414L16.414 11l1.586 1.586a1 1 0 01-1.414 1.414L15 12.414l-1.586 1.586a1 1 0 01-1.414-1.414L13.586 11l-1.586-1.586A1 1 0 0112 8z"/>
                  )}
                </svg>
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={isMuted ? 0 : volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-20 md:w-16 h-2 md:h-1 bg-secondary rounded-lg appearance-none cursor-pointer touch-manipulation"
              />
            </div>

            {/* Queue Button */}
            {showQueueButton && onShowQueue && (
              <div className="relative">
                <button
                  onClick={onShowQueue}
                  className="text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation"
                  title={`Queue (${queue.length} tracks)`}
                >
                  <svg className="w-5 h-5 md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                  </svg>
                </button>
                {queue.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-teal-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                    {queue.length > 99 ? '99+' : queue.length}
                  </span>
                )}
              </div>
            )}

            {/* Playlist Button */}
            {showPlaylistButton && onShowPlaylist && (
              <button
                onClick={onShowPlaylist}
                className="text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation"
                title={`Playlist (${playlist.length} tracks)`}
              >
                <svg className="w-5 h-5 md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              </button>
            )}

            {/* Equalizer Button */}
            {state.mediaPlayer.enableEqualizer && (
              <button
                onClick={() => setShowEqualizer(!showEqualizer)}
                className={`text-muted-foreground hover:text-card-foreground p-2 md:p-1 touch-manipulation ${
                  showEqualizer ? 'text-primary' : ''
                }`}
                title="Equalizer"
              >
                <svg className="w-5 h-5 md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Equalizer Panel */}
      {showEqualizer && (
        <div className="mt-4">
          <Equalizer
            audioElement={audioRef.current}
            enabled={state.mediaPlayer.enableEqualizer}
            preset={state.mediaPlayer.equalizerPreset}
            onPresetChange={(preset) => updateMediaPlayerSettings({ equalizerPreset: preset })}
          />
        </div>
      )}

      <audio ref={audioRef} preload="metadata" />
    </div>
  );
}
