'use client';

import React, { useState } from 'react';
import { MusicItem } from './MusicItem';

interface Track {
  name: string;
  title?: string;
  artist?: string;
  album?: string;
  url: string;
  duration?: number;
  picture?: string;
}

interface Playlist {
  id: string;
  name: string;
  tracks: Track[];
  createdAt: Date;
  updatedAt: Date;
}

interface PlaylistManagerProps {
  playlists: Playlist[];
  onCreatePlaylist: (name: string) => void;
  onDeletePlaylist: (id: string) => void;
  onRenamePlaylist: (id: string, newName: string) => void;
  onAddToPlaylist: (playlistId: string, track: Track) => void;
  onRemoveFromPlaylist: (playlistId: string, trackUrl: string) => void;
  onPlayPlaylist: (playlist: Playlist) => void;
  onPlayTrack: (track: Track) => void;
  className?: string;
}

export function PlaylistManager({
  playlists,
  onCreatePlaylist,
  onDeletePlaylist,
  onRenamePlaylist,
  onAddToPlaylist,
  onRemoveFromPlaylist,
  onPlayPlaylist,
  onPlayTrack,
  className = ''
}: PlaylistManagerProps) {
  const [selectedPlaylist, setSelectedPlaylist] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newPlaylistName, setNewPlaylistName] = useState('');
  const [editingPlaylist, setEditingPlaylist] = useState<string | null>(null);
  const [editName, setEditName] = useState('');

  const handleCreatePlaylist = () => {
    if (newPlaylistName.trim()) {
      onCreatePlaylist(newPlaylistName.trim());
      setNewPlaylistName('');
      setShowCreateForm(false);
    }
  };

  const handleRenamePlaylist = (id: string) => {
    if (editName.trim()) {
      onRenamePlaylist(id, editName.trim());
      setEditingPlaylist(null);
      setEditName('');
    }
  };

  const startEditing = (playlist: Playlist) => {
    setEditingPlaylist(playlist.id);
    setEditName(playlist.name);
  };

  const cancelEditing = () => {
    setEditingPlaylist(null);
    setEditName('');
  };

  const formatDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getTotalDuration = (tracks: Track[]) => {
    return tracks.reduce((total, track) => total + (track.duration || 0), 0);
  };

  const selectedPlaylistData = playlists.find(p => p.id === selectedPlaylist);

  return (
    <div className={`bg-gray-900 dark:bg-gray-950 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">Playlists</h2>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-1 rounded text-sm transition-colors"
        >
          + New Playlist
        </button>
      </div>

      {/* Create Playlist Form */}
      {showCreateForm && (
        <div className="mb-4 p-3 bg-gray-800 dark:bg-gray-900 rounded border">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newPlaylistName}
              onChange={(e) => setNewPlaylistName(e.target.value)}
              placeholder="Playlist name"
              className="flex-1 bg-gray-700 dark:bg-gray-800 text-white px-3 py-2 rounded border border-gray-600 focus:border-teal-500 focus:outline-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreatePlaylist();
                if (e.key === 'Escape') setShowCreateForm(false);
              }}
              autoFocus
            />
            <button
              onClick={handleCreatePlaylist}
              className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded text-sm"
            >
              Create
            </button>
            <button
              onClick={() => setShowCreateForm(false)}
              className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      <div className="flex h-96">
        {/* Playlist List */}
        <div className="w-1/3 border-r border-gray-700 pr-4">
          <div className="space-y-2 max-h-full overflow-y-auto">
            {playlists.map((playlist) => (
              <div
                key={playlist.id}
                className={`p-3 rounded cursor-pointer transition-colors ${
                  selectedPlaylist === playlist.id
                    ? 'bg-teal-600 text-white'
                    : 'bg-gray-800 dark:bg-gray-900 text-gray-300 hover:bg-gray-700 dark:hover:bg-gray-800'
                }`}
                onClick={() => setSelectedPlaylist(playlist.id)}
              >
                {editingPlaylist === playlist.id ? (
                  <div className="flex items-center space-x-1">
                    <input
                      type="text"
                      value={editName}
                      onChange={(e) => setEditName(e.target.value)}
                      className="flex-1 bg-gray-700 text-white px-2 py-1 rounded text-sm"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleRenamePlaylist(playlist.id);
                        if (e.key === 'Escape') cancelEditing();
                      }}
                      onBlur={() => handleRenamePlaylist(playlist.id)}
                      autoFocus
                    />
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-sm truncate">{playlist.name}</h3>
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onPlayPlaylist(playlist);
                          }}
                          className="text-gray-400 hover:text-white p-1"
                          title="Play playlist"
                        >
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8 5v10l8-5-8-5z"/>
                          </svg>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            startEditing(playlist);
                          }}
                          className="text-gray-400 hover:text-white p-1"
                          title="Rename playlist"
                        >
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm(`Delete playlist "${playlist.name}"?`)) {
                              onDeletePlaylist(playlist.id);
                              if (selectedPlaylist === playlist.id) {
                                setSelectedPlaylist(null);
                              }
                            }
                          }}
                          className="text-gray-400 hover:text-red-400 p-1"
                          title="Delete playlist"
                        >
                          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500">
                      {playlist.tracks.length} tracks • {formatDuration(getTotalDuration(playlist.tracks))}
                    </p>
                  </div>
                )}
              </div>
            ))}
            
            {playlists.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                <p>No playlists yet</p>
                <p className="text-sm">Create your first playlist!</p>
              </div>
            )}
          </div>
        </div>

        {/* Playlist Content */}
        <div className="flex-1 pl-4">
          {selectedPlaylistData ? (
            <div>
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-white">{selectedPlaylistData.name}</h3>
                <p className="text-sm text-gray-400">
                  {selectedPlaylistData.tracks.length} tracks • {formatDuration(getTotalDuration(selectedPlaylistData.tracks))}
                </p>
              </div>
              
              <div className="space-y-2 max-h-80 overflow-y-auto">
                {selectedPlaylistData.tracks.map((track, index) => (
                  <div
                    key={`${track.url}-${index}`}
                    className="flex items-center space-x-3 p-2 bg-gray-800 dark:bg-gray-900 rounded hover:bg-gray-700 dark:hover:bg-gray-800 transition-colors"
                  >
                    <button
                      onClick={() => onPlayTrack(track)}
                      className="text-gray-400 hover:text-white"
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M8 5v10l8-5-8-5z"/>
                      </svg>
                    </button>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-white truncate">{track.title || track.name}</p>
                      <p className="text-xs text-gray-400 truncate">{track.artist || 'Unknown Artist'}</p>
                    </div>
                    
                    {track.duration && (
                      <span className="text-xs text-gray-500">
                        {formatDuration(track.duration)}
                      </span>
                    )}
                    
                    <button
                      onClick={() => onRemoveFromPlaylist(selectedPlaylistData.id, track.url)}
                      className="text-gray-400 hover:text-red-400"
                      title="Remove from playlist"
                    >
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
                
                {selectedPlaylistData.tracks.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <p>This playlist is empty</p>
                    <p className="text-sm">Add tracks using the context menu</p>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <svg className="w-12 h-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
                <p>Select a playlist to view its contents</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
