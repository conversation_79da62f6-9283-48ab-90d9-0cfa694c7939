'use client';

import React from 'react';
import { Sheet, She<PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger } from '@/components/ui/Sheet';
import { Button } from '@/components/ui/Button';
import { Menu, Home, Settings, Music, Radio, Disc } from 'lucide-react';

interface MobileNavigationProps {
  currentView: string;
  onViewChange: (view: string) => void;
  className?: string;
}

export function MobileNavigation({ currentView, onViewChange, className = '' }: MobileNavigationProps) {
  const navigationItems = [
    { id: 'collection', label: 'Collection', icon: Music },
    { id: 'playlists', label: 'Playlists', icon: Disc },
    { id: 'network', label: 'Network', icon: Radio },
    { id: 'isos', label: 'ISO Files', icon: Disc },
    { id: 'settings', label: 'Settings', icon: Settings },
  ];

  return (
    <div className={`md:hidden ${className}`}>
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="h-10 w-10">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[280px] sm:w-[350px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Home className="h-5 w-5" />
              Wayne's Media Player
            </SheetTitle>
          </SheetHeader>
          <nav className="mt-6">
            <div className="space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <Button
                    key={item.id}
                    variant={currentView === item.id ? "default" : "ghost"}
                    className="w-full justify-start"
                    onClick={() => onViewChange(item.id)}
                  >
                    <Icon className="mr-2 h-4 w-4" />
                    {item.label}
                  </Button>
                );
              })}
            </div>
          </nav>
        </SheetContent>
      </Sheet>
    </div>
  );
}
