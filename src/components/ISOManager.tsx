'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/Tabs';
import { ISOParser, ISOAlbum, ISOTrack } from '@/lib/iso-parser';
import { Upload, Disc, Play, Download, FolderOpen } from 'lucide-react';

interface ISOManagerProps {
  onTrackPlay?: (track: ISOTrack) => void;
  onAlbumAdd?: (album: ISOAlbum) => void;
  className?: string;
}

export function ISOManager({ onTrackPlay, onAlbumAdd, className = '' }: ISOManagerProps) {
  const [albums, setAlbums] = useState<ISOAlbum[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAlbum, setSelectedAlbum] = useState<ISOAlbum | null>(null);

  console.log('ISOManager rendered with props:', { onTrackPlay, onAlbumAdd });

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    console.log('File upload triggered, files:', files);

    if (!files || files.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      const newAlbums: ISOAlbum[] = [];

      for (const file of Array.from(files)) {
        console.log('Processing file:', file.name, 'size:', file.size);

        if (!file.name.toLowerCase().endsWith('.iso')) {
          console.warn(`Skipping non-ISO file: ${file.name}`);
          continue;
        }

        console.log('Creating ISO parser for:', file.name);
        const parser = new ISOParser(file);
        console.log('Parsing ISO file...');
        const album = await parser.parse();
        console.log('Parsed album:', album);
        newAlbums.push(album);
      }

      console.log('All albums parsed:', newAlbums);
      setAlbums(prev => [...prev, ...newAlbums]);

      if (newAlbums.length > 0) {
        setSelectedAlbum(newAlbums[0]);
        console.log('Selected first album:', newAlbums[0]);
      }
    } catch (err) {
      console.error('Error parsing ISO files:', err);
      setError(err instanceof Error ? err.message : 'Failed to parse ISO files');
    } finally {
      setLoading(false);
    }
  }, []);

  const handleTrackPlay = useCallback((track: ISOTrack) => {
    console.log('ISOManager: handleTrackPlay called with track:', track);
    onTrackPlay?.(track);
  }, [onTrackPlay]);

  const handleAlbumAdd = useCallback((album: ISOAlbum) => {
    console.log('ISOManager: handleAlbumAdd called with album:', album);
    onAlbumAdd?.(album);
  }, [onAlbumAdd]);

  // Test function to create a demo track
  const createTestTrack = useCallback(() => {
    console.log('Creating test track...');

    // Create demo audio
    const sampleRate = 44100;
    const duration = 10; // 10 seconds
    const samples = Math.floor(sampleRate * duration);
    const buffer = new ArrayBuffer(44 + samples * 2);
    const view = new DataView(buffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + samples * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, 1, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * 2, true);
    view.setUint16(32, 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, samples * 2, true);

    // Generate a simple tone
    for (let i = 0; i < samples; i++) {
      const t = i / sampleRate;
      const sample = Math.sin(2 * Math.PI * 440 * t) * 0.3; // A4 note
      view.setInt16(44 + i * 2, sample * 16384, true);
    }

    const audioBlob = new Blob([buffer], { type: 'audio/wav' });
    const url = URL.createObjectURL(audioBlob);

    console.log('Test track created with URL:', url);

    const testTrack: ISOTrack = {
      id: 'test-track',
      title: 'Test Track',
      trackNumber: 1,
      url: url,
      format: 'WAV',
      duration: duration,
      sampleRate: sampleRate,
      bitDepth: 16,
      channels: 1
    };

    console.log('Playing test track:', testTrack);
    handleTrackPlay(testTrack);
  }, [handleTrackPlay]);

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Disc className="h-5 w-5" />
            ISO Manager
          </CardTitle>
          <CardDescription>
            Import and manage SACD, DVD-Audio, and CD ISO files
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* File Upload */}
          <div className="mb-6">
            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
              <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Upload ISO Files</h3>
                <p className="text-sm text-muted-foreground">
                  Select SACD, DVD-Audio, or CD ISO files to import
                </p>
                <div className="flex justify-center gap-2">
                  <Button asChild>
                    <label htmlFor="iso-upload" className="cursor-pointer">
                      <FolderOpen className="mr-2 h-4 w-4" />
                      Choose Files
                      <input
                        id="iso-upload"
                        type="file"
                        multiple
                        accept=".iso"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                    </label>
                  </Button>
                  <Button onClick={createTestTrack} variant="outline">
                    <Play className="mr-2 h-4 w-4" />
                    Test Audio
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Parsing ISO files...</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <Card className="border-destructive">
              <CardContent className="p-4">
                <p className="text-sm text-destructive">{error}</p>
              </CardContent>
            </Card>
          )}

          {/* Albums List */}
          {albums.length > 0 && (
            <Tabs value={selectedAlbum?.id} onValueChange={(id) => {
              const album = albums.find(a => a.id === id);
              setSelectedAlbum(album || null);
            }}>
              <TabsList className="grid w-full grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                {albums.map((album) => (
                  <TabsTrigger key={album.id} value={album.id} className="text-left">
                    <div className="truncate">
                      <div className="font-medium truncate">{album.title}</div>
                      <div className="text-xs text-muted-foreground">
                        {album.format} • {album.tracks.length} tracks
                      </div>
                    </div>
                  </TabsTrigger>
                ))}
              </TabsList>

              {albums.map((album) => (
                <TabsContent key={album.id} value={album.id} className="mt-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Album Info */}
                    <Card>
                      <CardContent className="p-4">
                        {album.coverArt && (
                          <img
                            src={album.coverArt}
                            alt={album.title}
                            className="w-full aspect-square object-cover rounded-lg mb-4"
                          />
                        )}
                        <h3 className="font-semibold text-lg mb-2">{album.title}</h3>
                        {album.artist && (
                          <p className="text-muted-foreground mb-2">{album.artist}</p>
                        )}
                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span>Format:</span>
                            <span className="font-medium">{album.format}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Tracks:</span>
                            <span className="font-medium">{album.tracks.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Duration:</span>
                            <span className="font-medium">{formatDuration(album.totalDuration)}</span>
                          </div>
                          {album.year && (
                            <div className="flex justify-between">
                              <span>Year:</span>
                              <span className="font-medium">{album.year}</span>
                            </div>
                          )}
                        </div>
                        <div className="mt-4 space-y-2">
                          <Button 
                            onClick={() => handleAlbumAdd(album)} 
                            className="w-full"
                          >
                            <Download className="mr-2 h-4 w-4" />
                            Add to Collection
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Track List */}
                    <Card className="lg:col-span-2">
                      <CardHeader>
                        <CardTitle>Tracks</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ScrollArea className="h-96">
                          <div className="space-y-2">
                            {album.tracks.map((track, index) => (
                              <div
                                key={track.id}
                                className="flex items-center p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground transition-colors"
                              >
                                <div className="w-8 text-center text-sm text-muted-foreground">
                                  {track.trackNumber}
                                </div>
                                <div className="flex-1 min-w-0 mx-3">
                                  <div className="font-medium truncate">{track.title}</div>
                                  {track.artist && track.artist !== album.artist && (
                                    <div className="text-sm text-muted-foreground truncate">
                                      {track.artist}
                                    </div>
                                  )}
                                  <div className="text-xs text-muted-foreground">
                                    {track.format}
                                    {track.sampleRate && ` • ${(track.sampleRate / 1000).toFixed(1)}kHz`}
                                    {track.bitDepth && ` • ${track.bitDepth}bit`}
                                    {track.channels && ` • ${track.channels}ch`}
                                  </div>
                                </div>
                                <div className="text-sm text-muted-foreground mr-3">
                                  {track.duration ? formatDuration(track.duration) : '--:--'}
                                </div>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleTrackPlay(track)}
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          )}

          {/* Empty State */}
          {albums.length === 0 && !loading && (
            <div className="text-center py-8">
              <Disc className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No ISO files loaded</h3>
              <p className="text-sm text-muted-foreground">
                Upload ISO files to browse and play individual tracks
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
