'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { MobileNavigation } from './MobileNavigation';
import { Search, MoreVertical, ListMusic, List } from 'lucide-react';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/Sheet';

interface MobileHeaderProps {
  currentView: string;
  onViewChange: (view: string) => void;
  onShowQueue?: () => void;
  onShowPlaylist?: () => void;
  queueCount?: number;
  playlistCount?: number;
  className?: string;
}

export function MobileHeader({ 
  currentView, 
  onViewChange, 
  onShowQueue,
  onShowPlaylist,
  queueCount = 0,
  playlistCount = 0,
  className = '' 
}: MobileHeaderProps) {
  const getViewTitle = (view: string) => {
    switch (view) {
      case 'collection': return 'Music Collection';
      case 'playlists': return 'Playlists';
      case 'network': return 'Network Sources';
      case 'settings': return 'Settings';
      default: return 'Wayne\'s Media Player';
    }
  };

  return (
    <header className={`bg-background border-b border-border px-4 py-3 md:hidden ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left: Navigation Menu */}
        <MobileNavigation currentView={currentView} onViewChange={onViewChange} />
        
        {/* Center: Title */}
        <h1 className="text-lg font-semibold truncate mx-4 flex-1 text-center">
          {getViewTitle(currentView)}
        </h1>
        
        {/* Right: Actions */}
        <div className="flex items-center space-x-2">
          {/* Queue Button */}
          {onShowQueue && (
            <div className="relative">
              <Button variant="ghost" size="icon" onClick={onShowQueue}>
                <ListMusic className="h-5 w-5" />
                <span className="sr-only">Queue</span>
              </Button>
              {queueCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {queueCount > 99 ? '99+' : queueCount}
                </span>
              )}
            </div>
          )}
          
          {/* Playlist Button */}
          {onShowPlaylist && (
            <Button variant="ghost" size="icon" onClick={onShowPlaylist}>
              <List className="h-5 w-5" />
              <span className="sr-only">Playlist</span>
            </Button>
          )}
          
          {/* More Options */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-5 w-5" />
                <span className="sr-only">More options</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[280px]">
              <SheetHeader>
                <SheetTitle>Options</SheetTitle>
              </SheetHeader>
              <div className="mt-6 space-y-4">
                <Button variant="ghost" className="w-full justify-start">
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </Button>
                <div className="border-t pt-4">
                  <p className="text-sm text-muted-foreground">
                    Queue: {queueCount} tracks
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Playlist: {playlistCount} tracks
                  </p>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
