import React from 'react';
import { spacing, typography, colors, utils, layout } from '@/lib/design-system';
import type { SettingsSectionProps } from '@/types/design-system';

export function SettingsSection({
  title,
  description,
  children,
  spacing: spacingVariant = 'normal',
  className = ''
}: SettingsSectionProps) {
  const spacingMap = {
    compact: 'space-y-2 md:space-y-3',
    normal: 'space-y-3 md:space-y-4',
    relaxed: 'space-y-4 md:space-y-6'
  };

  const sectionClasses = utils.cn(
    spacingMap[spacingVariant],
    className
  );

  const titleClasses = utils.cn(
    typography.text.base,
    'md:text-lg',
    typography.weight.medium,
    colors.text.primary,
    'border-b border-border pb-2 mb-3 md:mb-4'
  );

  const descriptionClasses = utils.cn(
    typography.text.sm,
    'md:text-base',
    colors.text.tertiary,
    'mt-1 mb-3 md:mb-4'
  );

  const contentClasses = utils.cn(
    spacingMap[spacingVariant]
  );

  return (
    <section className={sectionClasses} role="group" aria-labelledby={`section-${title.toLowerCase().replace(/\s+/g, '-')}`}>
      <div>
        <h3
          id={`section-${title.toLowerCase().replace(/\s+/g, '-')}`}
          className={titleClasses}
        >
          {title}
        </h3>
        {description && (
          <p className={descriptionClasses}>
            {description}
          </p>
        )}
      </div>
      <div className={contentClasses}>
        {children}
      </div>
    </section>
  );
}
