'use client';

import React from 'react';
import { useSettings } from './SettingsContext';
import { useTheme } from '@/components/ThemeProvider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export function AppearanceSettings() {
  const { updateAppearanceSettings } = useSettings();
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'dark', label: 'Dark', description: 'Dark theme optimized for low-light environments' },
    { value: 'light', label: 'Light', description: 'Light theme for bright environments' },
    { value: 'auto', label: 'Auto', description: 'Automatically switch based on system preference' },
  ];

  const handleThemeChange = async (newTheme: 'dark' | 'light' | 'auto') => {
    // Update both the theme provider and settings context
    setTheme(newTheme);
    await updateAppearanceSettings({ theme: newTheme });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Appearance</CardTitle>
        <CardDescription>
          Customize the look and feel of your media player.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Theme Selection */}
        <div>
          <h3 className="text-sm font-medium mb-3">Theme</h3>
          <div className="grid grid-cols-1 gap-3">
            {themes.map((themeOption) => (
              <Button
                key={themeOption.value}
                variant={theme === themeOption.value ? "default" : "outline"}
                className="justify-start h-auto p-4"
                onClick={() => handleThemeChange(themeOption.value as 'dark' | 'light' | 'auto')}
              >
                <div className="text-left">
                  <div className="font-medium">{themeOption.label}</div>
                  <div className="text-sm text-muted-foreground">{themeOption.description}</div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Additional appearance settings */}
        <div>
          <h3 className="text-sm font-medium mb-3">Additional Options</h3>
          <Card>
            <CardContent className="p-4">
              <p className="text-sm text-muted-foreground text-center">
                More appearance customization options will be added here
              </p>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
}
