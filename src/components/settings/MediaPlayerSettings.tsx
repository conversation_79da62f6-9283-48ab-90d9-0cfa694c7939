'use client';

import React, { useState } from 'react';
import { useSettings } from './SettingsContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Switch } from '@/components/ui/Switch';
import { Slider } from '@/components/ui/Slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/Tabs';

export function MediaPlayerSettings() {
  const { state, updateMediaPlayerSettings } = useSettings();
  const { mediaPlayer } = state;
  const [showEqualizerPresets, setShowEqualizerPresets] = useState(false);

  const playbackQualities = [
    { value: 'auto', label: 'Auto', description: 'Automatically selects the highest available quality for each track' },
    { value: 'low', label: 'Low (128 kbps)', description: 'Saves bandwidth, lower quality' },
    { value: 'medium', label: 'Medium (256 kbps)', description: 'Balanced quality and size' },
    { value: 'high', label: 'High (320 kbps)', description: 'High quality, larger files' },
    { value: 'lossless', label: 'Lossless', description: 'Original quality, largest files' },
  ];

  const equalizerPresets = [
    'flat', 'rock', 'pop', 'jazz', 'classical', 'electronic', 'hip-hop', 'acoustic', 'vocal', 'bass-boost'
  ];

  const bufferSizes = [
    { value: 1024, label: '1KB (Low latency)' },
    { value: 2048, label: '2KB (Balanced)' },
    { value: 4096, label: '4KB (Recommended)' },
    { value: 8192, label: '8KB (High quality)' },
    { value: 16384, label: '16KB (Maximum)' },
  ];

  const handleVolumeChange = async (volume: number) => {
    await updateMediaPlayerSettings({ defaultVolume: volume });
  };

  const handleQualityChange = async (quality: string) => {
    await updateMediaPlayerSettings({ playbackQuality: quality as any });
  };

  const handleCrossfadeToggle = async () => {
    await updateMediaPlayerSettings({ enableCrossfade: !mediaPlayer.enableCrossfade });
  };

  const handleCrossfadeDurationChange = async (duration: number) => {
    await updateMediaPlayerSettings({ crossfadeDuration: duration });
  };

  const handleEqualizerToggle = async () => {
    await updateMediaPlayerSettings({ enableEqualizer: !mediaPlayer.enableEqualizer });
  };

  const handleGaplessToggle = async () => {
    await updateMediaPlayerSettings({ enableGaplessPlayback: !mediaPlayer.enableGaplessPlayback });
  };

  const handleReplayGainToggle = async () => {
    await updateMediaPlayerSettings({ enableReplayGain: !mediaPlayer.enableReplayGain });
  };

  const handleVisualizationToggle = async () => {
    await updateMediaPlayerSettings({ enableVisualization: !mediaPlayer.enableVisualization });
  };

  const handleEqualizerPresetChange = async (preset: string) => {
    await updateMediaPlayerSettings({ equalizerPreset: preset });
    setShowEqualizerPresets(false);
  };

  const handleBufferSizeChange = async (size: number) => {
    await updateMediaPlayerSettings({ bufferSize: size });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Media Player</CardTitle>
        <CardDescription>
          Configure audio playback and quality settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="audio" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="audio">Audio</TabsTrigger>
            <TabsTrigger value="effects">Effects</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="audio" className="space-y-6">
            {/* Volume Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Volume & Quality</h3>

              <div className="space-y-2">
                <div className="flex justify-between">
                  <label className="text-sm font-medium">Default Volume</label>
                  <span className="text-sm text-muted-foreground">{mediaPlayer.defaultVolume}%</span>
                </div>
                <Slider
                  value={[mediaPlayer.defaultVolume]}
                  onValueChange={(value) => handleVolumeChange(value[0])}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Playback Quality</label>
                <div className="grid grid-cols-1 gap-2">
                  {playbackQualities.map((quality) => (
                    <Button
                      key={quality.value}
                      variant={mediaPlayer.playbackQuality === quality.value ? "default" : "outline"}
                      className="justify-start h-auto p-3"
                      onClick={() => handleQualityChange(quality.value)}
                    >
                      <div className="text-left">
                        <div className="font-medium">{quality.label}</div>
                        <div className="text-sm text-muted-foreground">{quality.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="effects" className="space-y-6">
            {/* Audio Effects */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Audio Effects</h3>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="text-sm font-medium">Crossfade</label>
                  <p className="text-sm text-muted-foreground">Smooth transitions between tracks</p>
                </div>
                <Switch
                  checked={mediaPlayer.enableCrossfade}
                  onCheckedChange={handleCrossfadeToggle}
                />
              </div>

              {mediaPlayer.enableCrossfade && (
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <label className="text-sm font-medium">Crossfade Duration</label>
                    <span className="text-sm text-muted-foreground">{mediaPlayer.crossfadeDuration}s</span>
                  </div>
                  <Slider
                    value={[mediaPlayer.crossfadeDuration]}
                    onValueChange={(value) => handleCrossfadeDurationChange(value[0])}
                    min={1}
                    max={10}
                    step={0.5}
                    className="w-full"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="text-sm font-medium">Equalizer</label>
                  <p className="text-sm text-muted-foreground">Adjust audio frequencies</p>
                </div>
                <Switch
                  checked={mediaPlayer.enableEqualizer}
                  onCheckedChange={handleEqualizerToggle}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-6">
            {/* Advanced Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Advanced Options</h3>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="text-sm font-medium">Gapless Playback</label>
                  <p className="text-sm text-muted-foreground">No silence between tracks</p>
                </div>
                <Switch
                  checked={mediaPlayer.enableGaplessPlayback}
                  onCheckedChange={handleGaplessToggle}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="text-sm font-medium">ReplayGain</label>
                  <p className="text-sm text-muted-foreground">Normalize volume levels</p>
                </div>
                <Switch
                  checked={mediaPlayer.enableReplayGain}
                  onCheckedChange={handleReplayGainToggle}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <label className="text-sm font-medium">Visualization</label>
                  <p className="text-sm text-muted-foreground">Audio visualization effects</p>
                </div>
                <Switch
                  checked={mediaPlayer.enableVisualization}
                  onCheckedChange={handleVisualizationToggle}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
