'use client';

import React, { useState } from 'react';
import { useSettings } from './SettingsContext';
import { SupportedFormatsPopup } from '@/components/ui/SupportedFormatsPopup';
import { DirectoryBrowserModal } from '@/components/ui/DirectoryBrowser';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

export function MusicDirectorySettings() {
  const { state, addMusicDirectory, removeMusicDirectory } = useSettings();
  const [newDir, setNewDir] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [showBrowser, setShowBrowser] = useState(false);

  const validateDirectory = (dir: string): string => {
    if (!dir.trim()) return 'Directory path cannot be empty';
    if (state.musicDirs.includes(dir.trim())) return 'Directory already exists';
    if (dir.length > 500) return 'Directory path is too long';

    // Security checks
    if (dir.trim() === '/' || dir.trim() === 'C:\\' || dir.trim() === 'C:/') {
      return 'Root directory access is restricted for security reasons';
    }

    // Prevent access to system directories
    const restrictedPaths = ['/etc', '/sys', '/proc', '/dev', '/boot', 'C:\\Windows', 'C:\\System32'];
    if (restrictedPaths.some(restricted => dir.trim().toLowerCase().startsWith(restricted.toLowerCase()))) {
      return 'Access to system directories is not allowed';
    }

    // Basic path validation
    if (!/^[a-zA-Z]:|^\/|^\\/.test(dir.trim())) {
      return 'Please enter a valid absolute path';
    }

    return '';
  };

  const handleAddDirectory = async () => {
    const trimmedDir = newDir.trim();
    const error = validateDirectory(trimmedDir);

    if (error) {
      setValidationError(error);
      return;
    }

    setIsAdding(true);
    setValidationError('');
    try {
      await addMusicDirectory(trimmedDir);
      setNewDir('');
    } finally {
      setIsAdding(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddDirectory();
    }
  };

  const handleInputChange = (value: string) => {
    setNewDir(value);
    if (validationError) {
      setValidationError('');
    }
  };

  const handleRemoveDirectory = async (dir: string) => {
    if (confirm(`Are you sure you want to remove "${dir}" from your music directories?`)) {
      await removeMusicDirectory(dir);
    }
  };

  const handleBrowserSelect = async (path: string) => {
    setNewDir(path);
    setShowBrowser(false);

    // Auto-add if valid
    const error = validateDirectory(path);
    if (!error) {
      setIsAdding(true);
      setValidationError('');
      try {
        await addMusicDirectory(path);
        setNewDir('');
      } catch (err) {
        setValidationError('Failed to add directory');
      } finally {
        setIsAdding(false);
      }
    } else {
      setValidationError(error);
    }
  };

  return (
    <div className="bg-card rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-2">Music Directories</h2>
        <p className="text-muted-foreground text-sm">
          Add directories where your music files are stored. The app will scan these locations for audio files.
        </p>
      </div>

      {/* Add Directory Form */}
      <div className="mb-6">
        <label htmlFor="new-directory" className="block text-sm font-medium text-card-foreground mb-2">
          Add New Directory
        </label>
        <div className="space-y-2">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
            <Input
              id="new-directory"
              value={newDir}
              onChange={(e) => handleInputChange(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="/path/to/your/music"
              disabled={isAdding}
              className={`flex-1 ${validationError ? 'border-destructive' : ''}`}
            />
            <div className="flex space-x-2">
              <Button
                variant="secondary"
                onClick={() => setShowBrowser(true)}
                disabled={isAdding}
                className="flex items-center space-x-2"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                </svg>
                <span>Browse</span>
              </Button>
              <Button
                variant="default"
                onClick={handleAddDirectory}
                disabled={isAdding || !newDir.trim()}
              >
                {isAdding ? 'Adding...' : 'Add'}
              </Button>
            </div>
          </div>
          {validationError && (
            <p className="text-sm text-destructive">{validationError}</p>
          )}
        </div>

      </div>

      {/* Directory List */}
      <div>
        <h3 className="text-lg font-medium text-card-foreground mb-3">
          Current Directories ({state.musicDirs.length})
        </h3>

        {state.musicDirs.length === 0 ? (
          <div className="text-center py-8 bg-secondary rounded-md">
            <svg className="mx-auto h-12 w-12 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-card-foreground">No music directories</h3>
            <p className="mt-1 text-sm text-muted-foreground">
              Add a directory above to start organizing your music collection.
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {state.musicDirs.map((dir, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-secondary rounded-md hover:bg-accent transition-colors"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <svg className="h-5 w-5 text-muted-foreground flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                  </svg>
                  <span className="text-card-foreground truncate" title={dir}>
                    {dir}
                  </span>
                </div>

                <button
                  onClick={() => handleRemoveDirectory(dir)}
                  className="ml-3 p-1 text-muted-foreground hover:text-destructive focus:outline-none focus:ring-2 focus:ring-destructive rounded"
                  title={`Remove ${dir}`}
                >
                  <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Help Text */}
      <div className="mt-6 p-4 bg-blue-900 bg-opacity-50 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-300">Tips</h3>
            <div className="mt-2 text-sm text-blue-200">
              <ul className="list-disc list-inside space-y-1">
                <li>Use absolute paths (e.g., /home/<USER>/Music or C:\Users\<USER>\Music)</li>
                <li>The app will recursively scan subdirectories</li>
                <li className="flex items-center">
                  <span>Supported formats: MP3, FLAC, WAV, OGG, M4A, and more</span>
                  <SupportedFormatsPopup className="ml-2" />
                </li>
                <li>Changes are saved automatically</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Directory Browser Modal */}
      <DirectoryBrowserModal
        isOpen={showBrowser}
        onClose={() => setShowBrowser(false)}
        onCancel={() => setShowBrowser(false)}
        onSelect={handleBrowserSelect}
        mode="directory"
        initialPath="/home"
      />
    </div>
  );
}
