'use client';

import React, { useState, useRef } from 'react';
import { useSettings } from './SettingsContext';
import { SettingsCard } from './SettingsCard';
import { SettingsSection } from './SettingsSection';
import { SettingsRow } from './SettingsRow';
import { Button } from '../ui/Button';
import { DownloadIcon, AddIcon, CheckIcon, CloseIcon } from '../ui/Icons';

export function ExportImportSettings() {
  const { exportSettings, importSettings, state } = useSettings();
  const [importStatus, setImportStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [importError, setImportError] = useState('');
  const [isImporting, setIsImporting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExportSettings = () => {
    try {
      const settingsData = exportSettings();
      const blob = new Blob([settingsData], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `waynes-media-player-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export settings:', error);
    }
  };

  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportStatus('idle');
    setImportError('');

    try {
      const text = await file.text();
      await importSettings(text);
      setImportStatus('success');
      setTimeout(() => setImportStatus('idle'), 3000);
    } catch (error) {
      setImportStatus('error');
      setImportError((error as Error).message);
      setTimeout(() => {
        setImportStatus('idle');
        setImportError('');
      }, 5000);
    } finally {
      setIsImporting(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const getImportButtonContent = () => {
    if (isImporting) {
      return (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          Importing...
        </>
      );
    }

    switch (importStatus) {
      case 'success':
        return (
          <>
            <CheckIcon className="w-4 h-4" />
            Imported Successfully
          </>
        );
      case 'error':
        return (
          <>
            <CloseIcon className="w-4 h-4" />
            Import Failed
          </>
        );
      default:
        return (
          <>
            <AddIcon className="w-4 h-4" />
            Import Settings
          </>
        );
    }
  };

  const getImportButtonVariant = () => {
    switch (importStatus) {
      case 'success':
        return 'default';
      case 'error':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <SettingsCard
      title="Backup & Restore"
      description="Export and import your settings for easy migration between devices"
      icon={<DownloadIcon className="w-5 h-5" />}
    >
      <div className="space-y-6">
        <SettingsSection title="Export Settings">
          <SettingsRow
            label="Download Settings"
            description="Export all your settings to a JSON file for backup or migration"
            action={
              <Button
                variant="default"
                onClick={handleExportSettings}
                className="flex items-center space-x-2"
              >
                <DownloadIcon className="w-4 h-4" />
                <span>Export Settings</span>
              </Button>
            }
          />
        </SettingsSection>

        <SettingsSection title="Import Settings">
          <SettingsRow
            label="Upload Settings"
            description="Import settings from a previously exported JSON file"
            action={
              <div className="space-y-2">
                <Button
                  variant={getImportButtonVariant()}
                  onClick={handleImportClick}
                  disabled={isImporting}
                  className="flex items-center space-x-2"
                >
                  {getImportButtonContent()}
                </Button>
                
                {importError && (
                  <p className="text-sm text-red-400">
                    {importError}
                  </p>
                )}
                
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>
            }
          />
        </SettingsSection>

        <SettingsSection title="What's Included">
          <div className="text-sm text-gray-400 space-y-2">
            <p className="font-medium text-gray-300">Settings that will be exported/imported:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Helper app URL configuration</li>
              <li>Theme preferences (dark/light/auto)</li>
              <li>Network discovery settings</li>
              <li>Network sources and connections</li>
              <li>Media player preferences (volume, quality, equalizer, etc.)</li>
              <li>Keyboard shortcuts and hotkeys</li>
            </ul>
            
            <p className="font-medium text-gray-300 mt-4">Settings that are NOT included:</p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Music directories (device-specific paths)</li>
              <li>Cached media files or metadata</li>
              <li>Temporary session data</li>
            </ul>
            
            <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-700/30 rounded-lg">
              <p className="text-yellow-300 text-sm">
                <strong>Note:</strong> After importing settings, you may need to reconfigure your music directories 
                as file paths are device-specific and not included in the backup.
              </p>
            </div>
          </div>
        </SettingsSection>
      </div>
    </SettingsCard>
  );
}
