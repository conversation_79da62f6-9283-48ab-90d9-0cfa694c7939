'use client';

import React, { useState, useEffect } from 'react';
import { useSettings } from './SettingsContext';

export function NetworkSettings() {
  const { state, updateNetworkSettings } = useSettings();
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredDevices, setDiscoveredDevices] = useState<any[]>([]);
  const [lastScanTime, setLastScanTime] = useState<Date | null>(null);

  // Auto-discovery functionality
  const performNetworkScan = async () => {
    setIsScanning(true);
    try {
      // Get helper app URL from localStorage or use default
      const helperAppUrl = localStorage.getItem('helperAppUrl') || 'http://localhost:3001';

      const response = await fetch(`${helperAppUrl}/api/network/discover`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          scanTypes: ['upnp', 'dlna', 'smb', 'nfs'],
          timeout: 10000 // 10 second timeout
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setDiscoveredDevices(data.devices || []);
        setLastScanTime(new Date());

        // Update network sources in settings
        if (data.devices && data.devices.length > 0) {
          const networkSources = data.devices.map((device: any) => ({
            id: device.id || `${device.type}-${device.ip}`,
            name: device.name || device.ip,
            type: device.type,
            url: device.url,
            ip: device.ip,
            port: device.port,
            discovered: true
          }));

          await updateNetworkSettings({ networkSources });
        }
      } else {
        console.error('Network scan failed:', response.statusText);
      }
    } catch (error) {
      console.error('Network discovery error:', error);
    } finally {
      setIsScanning(false);
    }
  };

  // Auto-scan when auto-discovery is enabled
  useEffect(() => {
    if (state.autoDiscovery && !isScanning) {
      // Perform initial scan after a short delay
      const timeoutId = setTimeout(() => {
        performNetworkScan();
      }, 2000);

      return () => clearTimeout(timeoutId);
    }
  }, [state.autoDiscovery]);

  // Handle manual re-scan
  const handleReScan = () => {
    performNetworkScan();
  };

  return (
    <div className="bg-gray-900 rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-white mb-2">Network & Discovery</h2>
        <p className="text-gray-400 text-sm">
          Configure network media source discovery and streaming settings.
        </p>
      </div>
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-sm font-medium text-white">Auto-discover network sources</h3>
            <p className="text-sm text-gray-400">Automatically scan for UPnP/DLNA devices and network shares</p>
          </div>
          <button
            onClick={() => updateNetworkSettings({ autoDiscovery: !state.autoDiscovery })}
            className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:ring-offset-gray-900 ${
              state.autoDiscovery ? 'bg-teal-600' : 'bg-gray-600'
            }`}
          >
            <span
              className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                state.autoDiscovery ? 'translate-x-5' : 'translate-x-0'
              }`}
            />
          </button>
        </div>
        
        {state.autoDiscovery && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-white">Scan Network</h3>
                <p className="text-sm text-gray-400">Manually scan for network devices and media servers</p>
              </div>
              <button
                onClick={handleReScan}
                disabled={isScanning}
                className="bg-teal-600 hover:bg-teal-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm touch-manipulation flex items-center space-x-2"
              >
                {isScanning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    <span>Scanning...</span>
                  </>
                ) : (
                  <span>Re-Scan</span>
                )}
              </button>
            </div>

            {/* Scan Status */}
            {lastScanTime && (
              <div className="text-xs text-gray-500">
                Last scan: {lastScanTime.toLocaleTimeString()}
              </div>
            )}

            {/* Discovered Devices */}
            {discoveredDevices.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-white mb-2">Discovered Devices ({discoveredDevices.length})</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {discoveredDevices.map((device, index) => (
                    <div key={device.id || index} className="flex items-center justify-between p-2 bg-gray-800 rounded text-sm">
                      <div>
                        <div className="text-white font-medium">{device.name || device.ip}</div>
                        <div className="text-gray-400 text-xs">{device.type?.toUpperCase()} • {device.ip}:{device.port}</div>
                      </div>
                      <div className="text-green-400 text-xs">
                        ✓ Available
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* No devices found message */}
            {!isScanning && lastScanTime && discoveredDevices.length === 0 && (
              <div className="text-sm text-gray-400 italic">
                No network devices found. Make sure devices are powered on and connected to the same network.
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
