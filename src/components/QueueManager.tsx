'use client';

import React, { useState } from 'react';

interface Track {
  name: string;
  title?: string;
  artist?: string;
  album?: string;
  url: string;
  duration?: number;
  picture?: string;
}

interface QueueManagerProps {
  queue: Track[];
  currentTrackIndex: number;
  onPlayTrack: (index: number) => void;
  onRemoveFromQueue: (index: number) => void;
  onClearQueue: () => void;
  onReorderQueue: (fromIndex: number, toIndex: number) => void;
  className?: string;
}

export function QueueManager({
  queue,
  currentTrackIndex,
  onPlayTrack,
  onRemoveFromQueue,
  onClearQueue,
  onReorderQueue,
  className = ''
}: QueueManagerProps) {
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);

  const formatDuration = (duration?: number) => {
    if (!duration || !isFinite(duration)) return '';
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getTotalDuration = () => {
    return queue.reduce((total, track) => total + (track.duration || 0), 0);
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      onReorderQueue(draggedIndex, dropIndex);
    }
    
    setDraggedIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
  };

  return (
    <div className={`bg-card rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-semibold text-card-foreground">Queue</h2>
          <p className="text-sm text-muted-foreground">
            {queue.length} tracks • {formatDuration(getTotalDuration())}
          </p>
        </div>

        {queue.length > 0 && (
          <button
            onClick={onClearQueue}
            className="bg-destructive hover:bg-destructive/90 text-destructive-foreground px-3 py-1 rounded text-sm transition-colors"
          >
            Clear Queue
          </button>
        )}
      </div>

      {queue.length === 0 ? (
        <div className="text-center text-muted-foreground py-12">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
          </svg>
          <p className="text-lg mb-2">Queue is empty</p>
          <p className="text-sm">Add tracks to your queue using the context menu</p>
        </div>
      ) : (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {queue.map((track, index) => (
            <div
              key={`${track.url}-${index}`}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              onDragEnd={handleDragEnd}
              className={`flex items-center space-x-3 p-3 rounded transition-colors cursor-move ${
                index === currentTrackIndex
                  ? 'bg-primary text-primary-foreground'
                  : draggedIndex === index
                  ? 'bg-muted opacity-50'
                  : 'bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {/* Drag Handle */}
              <div className="text-muted-foreground cursor-move">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 16a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"/>
                </svg>
              </div>

              {/* Track Number */}
              <div className="w-8 text-center">
                {index === currentTrackIndex ? (
                  <div className="w-4 h-4 mx-auto">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l8-5-8-5z"/>
                    </svg>
                  </div>
                ) : (
                  <span className="text-sm text-gray-500">{index + 1}</span>
                )}
              </div>

              {/* Album Art */}
              <div className="w-10 h-10 bg-gray-700 dark:bg-gray-800 rounded flex-shrink-0">
                {track.picture ? (
                  <img 
                    src={track.picture} 
                    alt={track.title || track.name} 
                    className="w-full h-full object-cover rounded" 
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
                    </svg>
                  </div>
                )}
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <p className={`text-sm font-medium truncate ${
                  index === currentTrackIndex ? 'text-white' : 'text-gray-200'
                }`}>
                  {track.title || track.name}
                </p>
                <p className={`text-xs truncate ${
                  index === currentTrackIndex ? 'text-teal-100' : 'text-gray-400'
                }`}>
                  {track.artist || 'Unknown Artist'}
                  {track.album && ` • ${track.album}`}
                </p>
              </div>

              {/* Duration */}
              {track.duration && (
                <span className={`text-xs ${
                  index === currentTrackIndex ? 'text-teal-100' : 'text-gray-500'
                }`}>
                  {formatDuration(track.duration)}
                </span>
              )}

              {/* Actions */}
              <div className="flex items-center space-x-1">
                {index !== currentTrackIndex && (
                  <button
                    onClick={() => onPlayTrack(index)}
                    className="text-gray-400 hover:text-white p-1"
                    title="Play this track"
                  >
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l8-5-8-5z"/>
                    </svg>
                  </button>
                )}
                
                <button
                  onClick={() => onRemoveFromQueue(index)}
                  className="text-gray-400 hover:text-red-400 p-1"
                  title="Remove from queue"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Queue Stats */}
      {queue.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <div className="flex justify-between text-sm text-gray-400">
            <span>Up next: {queue.length - currentTrackIndex - 1} tracks</span>
            <span>Total: {formatDuration(getTotalDuration())}</span>
          </div>
        </div>
      )}
    </div>
  );
}
