'use client';

import React from 'react';
import { ContextMenu, useContextMenu, useLongPress } from './ContextMenu';

interface Track {
  name: string;
  title?: string;
  artist?: string;
  album?: string;
  url: string;
  duration?: number;
  picture?: string;
  type?: string;
}

interface MusicItemProps {
  track: Track;
  index: number;
  onPlay: (track: Track) => void;
  onAddToQueue?: (track: Track) => void;
  onAddToPlaylist?: (track: Track, playlistId?: string) => void;
  onPlayNext?: (track: Track) => void;
  onRemove?: (track: Track) => void;
  playlists?: Array<{ id: string; name: string }>;
  className?: string;
}

export function MusicItem({
  track,
  index,
  onPlay,
  onAddToQueue,
  onAddToPlaylist,
  onPlayNext,
  onRemove,
  playlists = [],
  className = ''
}: MusicItemProps) {
  const { contextMenu, showContextMenu, hideContextMenu } = useContextMenu();

  const handleContextMenu = (event: React.MouseEvent) => {
    const items = [
      {
        id: 'play',
        label: 'Play Now',
        icon: (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M8 5v10l8-5-8-5z"/>
          </svg>
        ),
        action: () => onPlay(track)
      },
      {
        id: 'play-next',
        label: 'Play Next',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        ),
        action: () => onPlayNext?.(track),
        disabled: !onPlayNext
      },
      {
        id: 'add-to-queue',
        label: 'Add to Queue',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        ),
        action: () => onAddToQueue?.(track),
        disabled: !onAddToQueue,
        separator: true
      },
      {
        id: 'add-to-playlist',
        label: 'Add to Playlist',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        ),
        action: () => {
          // For now, just add to a default playlist
          onAddToPlaylist?.(track);
        },
        disabled: !onAddToPlaylist
      },
      {
        id: 'remove',
        label: 'Remove',
        icon: (
          <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        ),
        action: () => onRemove?.(track),
        disabled: !onRemove,
        separator: true
      }
    ];

    showContextMenu(event, items);
  };

  const longPressProps = useLongPress((event) => {
    // Convert touch event to mouse event for context menu
    const mouseEvent = {
      ...event,
      clientX: (event as React.TouchEvent).touches?.[0]?.clientX || (event as React.MouseEvent).clientX,
      clientY: (event as React.TouchEvent).touches?.[0]?.clientY || (event as React.MouseEvent).clientY,
      preventDefault: event.preventDefault,
      stopPropagation: event.stopPropagation
    } as React.MouseEvent;
    
    handleContextMenu(mouseEvent);
  });

  const formatDuration = (duration?: number) => {
    if (!duration || !isFinite(duration)) return '';
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <>
      <div
        className={`bg-card border border-border rounded-lg overflow-hidden shadow-sm cursor-pointer touch-manipulation hover:bg-accent hover:text-accent-foreground transition-colors group ${className}`}
        onClick={() => onPlay(track)}
        onContextMenu={handleContextMenu}
        {...longPressProps}
      >
        <div className="w-full aspect-square bg-muted relative">
          {track.picture ? (
            <img
              src={track.picture}
              alt={track.title || track.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <svg className="w-8 h-8 text-muted-foreground" fill="currentColor" viewBox="0 0 20 20">
                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v9.114A4.369 4.369 0 005 14c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V7.82l8-1.6v5.894A4.37 4.37 0 0015 12c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2V3z"/>
              </svg>
            </div>
          )}

          {/* Play overlay on hover */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M8 5v10l8-5-8-5z"/>
            </svg>
          </div>

          {/* Duration overlay */}
          {track.duration && (
            <div className="absolute bottom-2 right-2 bg-black/75 text-white text-xs px-2 py-1 rounded">
              {formatDuration(track.duration)}
            </div>
          )}
        </div>

        <div className="p-3">
          <h3 className="font-medium text-sm truncate">
            {track.title || track.name}
          </h3>
          <p className="text-xs text-muted-foreground truncate mt-1">
            {track.artist || 'Unknown Artist'}
          </p>
          {track.album && (
            <p className="text-xs text-muted-foreground truncate mt-0.5">
              {track.album}
            </p>
          )}
        </div>
      </div>

      <ContextMenu
        items={contextMenu.items}
        x={contextMenu.x}
        y={contextMenu.y}
        visible={contextMenu.visible}
        onClose={hideContextMenu}
      />
    </>
  );
}
