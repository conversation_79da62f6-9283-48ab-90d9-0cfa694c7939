'use client';

import React, { useState, useEffect, useRef } from 'react';

interface EqualizerProps {
  audioElement?: HTMLAudioElement | null;
  enabled: boolean;
  preset: string;
  onPresetChange?: (preset: string) => void;
}

interface EqualizerBand {
  frequency: number;
  gain: number;
  label: string;
}

const FREQUENCY_BANDS: EqualizerBand[] = [
  { frequency: 60, gain: 0, label: '60Hz' },
  { frequency: 170, gain: 0, label: '170Hz' },
  { frequency: 310, gain: 0, label: '310Hz' },
  { frequency: 600, gain: 0, label: '600Hz' },
  { frequency: 1000, gain: 0, label: '1kHz' },
  { frequency: 3000, gain: 0, label: '3kHz' },
  { frequency: 6000, gain: 0, label: '6kHz' },
  { frequency: 12000, gain: 0, label: '12kHz' },
  { frequency: 14000, gain: 0, label: '14kHz' },
  { frequency: 16000, gain: 0, label: '16kHz' }
];

const EQUALIZER_PRESETS: Record<string, number[]> = {
  flat: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
  rock: [5, 3, -1, -2, -1, 2, 4, 5, 5, 5],
  pop: [-1, 2, 4, 4, 1, -1, -2, -2, -1, -1],
  jazz: [4, 3, 1, 2, -1, -1, 0, 1, 2, 3],
  classical: [5, 4, 3, 2, -1, -1, 0, 2, 3, 4],
  electronic: [5, 4, 1, 0, -1, 2, 1, 1, 4, 5],
  'hip-hop': [5, 4, 1, 3, -1, -1, 1, -1, 2, 3],
  acoustic: [4, 3, 2, 1, 2, 2, 3, 4, 4, 3],
  vocal: [-2, -1, -1, 1, 3, 3, 2, 1, 0, -1],
  'bass-boost': [7, 6, 5, 3, 1, -1, -2, -3, -3, -3]
};

export function Equalizer({ audioElement, enabled, preset, onPresetChange }: EqualizerProps) {
  const [bands, setBands] = useState<EqualizerBand[]>(FREQUENCY_BANDS);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [sourceNode, setSourceNode] = useState<MediaElementAudioSourceNode | null>(null);
  const [gainNodes, setGainNodes] = useState<BiquadFilterNode[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize Web Audio API
  const initializeAudioContext = () => {
    if (!audioElement || isInitialized) return;

    try {
      const context = new (window.AudioContext || (window as any).webkitAudioContext)();
      const source = context.createMediaElementSource(audioElement);
      
      // Create filter nodes for each frequency band
      const filters: BiquadFilterNode[] = [];
      
      bands.forEach((band, index) => {
        const filter = context.createBiquadFilter();
        filter.type = index === 0 ? 'lowshelf' : index === bands.length - 1 ? 'highshelf' : 'peaking';
        filter.frequency.value = band.frequency;
        filter.Q.value = 1;
        filter.gain.value = band.gain;
        
        if (index === 0) {
          source.connect(filter);
        } else {
          filters[index - 1].connect(filter);
        }
        
        filters.push(filter);
      });
      
      // Connect the last filter to the destination
      filters[filters.length - 1].connect(context.destination);
      
      setAudioContext(context);
      setSourceNode(source);
      setGainNodes(filters);
      setIsInitialized(true);
      
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
    }
  };

  // Apply preset
  const applyPreset = (presetName: string) => {
    const presetValues = EQUALIZER_PRESETS[presetName];
    if (!presetValues) return;

    const newBands = bands.map((band, index) => ({
      ...band,
      gain: presetValues[index] || 0
    }));

    setBands(newBands);
    
    // Apply to audio nodes
    gainNodes.forEach((node, index) => {
      if (node && presetValues[index] !== undefined) {
        node.gain.value = presetValues[index];
      }
    });
  };

  // Handle band gain change
  const handleBandChange = (index: number, gain: number) => {
    const newBands = [...bands];
    newBands[index].gain = gain;
    setBands(newBands);

    // Apply to audio node
    if (gainNodes[index]) {
      gainNodes[index].gain.value = gain;
    }

    // If we're changing from a preset, switch to custom
    if (preset !== 'custom' && onPresetChange) {
      onPresetChange('custom');
    }
  };

  // Initialize when audio element is available
  useEffect(() => {
    if (audioElement && enabled && !isInitialized) {
      // Wait for audio to be ready
      const handleCanPlay = () => {
        initializeAudioContext();
      };

      if (audioElement.readyState >= 2) {
        initializeAudioContext();
      } else {
        audioElement.addEventListener('canplay', handleCanPlay);
        return () => audioElement.removeEventListener('canplay', handleCanPlay);
      }
    }
  }, [audioElement, enabled, isInitialized]);

  // Apply preset when it changes
  useEffect(() => {
    if (preset && isInitialized) {
      applyPreset(preset);
    }
  }, [preset, isInitialized]);

  // Enable/disable equalizer
  useEffect(() => {
    if (gainNodes.length > 0) {
      gainNodes.forEach((node, index) => {
        if (enabled) {
          node.gain.value = bands[index].gain;
        } else {
          node.gain.value = 0;
        }
      });
    }
  }, [enabled, gainNodes, bands]);

  if (!enabled) {
    return (
      <div className="text-center text-gray-500 py-8">
        <p>Equalizer is disabled</p>
        <p className="text-sm">Enable it in the media player settings</p>
      </div>
    );
  }

  return (
    <div className="bg-gray-900 rounded-lg p-4">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-white mb-2">Equalizer</h3>
        <div className="flex flex-wrap gap-2">
          {Object.keys(EQUALIZER_PRESETS).map((presetName) => (
            <button
              key={presetName}
              onClick={() => onPresetChange?.(presetName)}
              className={`px-3 py-1 rounded text-sm capitalize transition-colors ${
                preset === presetName
                  ? 'bg-teal-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {presetName.replace('-', ' ')}
            </button>
          ))}
        </div>
      </div>

      <div className="flex items-end justify-between space-x-2 h-48">
        {bands.map((band, index) => (
          <div key={band.frequency} className="flex flex-col items-center space-y-2">
            <div className="text-xs text-gray-400 font-mono">
              {band.gain > 0 ? '+' : ''}{band.gain.toFixed(1)}dB
            </div>
            <input
              type="range"
              min="-12"
              max="12"
              step="0.5"
              value={band.gain}
              onChange={(e) => handleBandChange(index, parseFloat(e.target.value))}
              className="w-6 h-32 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-vertical"
              style={{
                writingMode: 'bt-lr' as any,
                WebkitAppearance: 'slider-vertical'
              } as React.CSSProperties}
            />
            <div className="text-xs text-gray-400 text-center">
              {band.label}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 text-center">
        <button
          onClick={() => applyPreset('flat')}
          className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm transition-colors"
        >
          Reset to Flat
        </button>
      </div>
    </div>
  );
}
