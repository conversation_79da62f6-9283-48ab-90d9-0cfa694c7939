'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { ScrollArea } from '@/components/ui/ScrollArea';

interface Track {
  url: string;
  name: string;
  artist?: string;
  album?: string;
  duration?: number;
  picture?: string;
}

interface PlaylistProps {
  tracks: Track[];
  currentTrackIndex: number;
  onTrackSelect: (index: number) => void;
  onTrackRemove?: (index: number) => void;
  onPlaylistClear?: () => void;
  className?: string;
}

export function Playlist({ 
  tracks, 
  currentTrackIndex, 
  onTrackSelect, 
  onTrackRemove, 
  onPlaylistClear,
  className = '' 
}: PlaylistProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const formatDuration = (duration?: number) => {
    if (!duration || !isFinite(duration)) return '';
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const totalDuration = tracks.reduce((total, track) => {
    return total + (track.duration || 0);
  }, 0);

  if (tracks.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <div className="text-muted-foreground">
            <svg className="mx-auto h-12 w-12 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
            </svg>
            <h3 className="text-lg font-medium text-muted-foreground mb-2">No tracks in playlist</h3>
            <p className="text-sm">Add some music to get started</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Current Playlist</CardTitle>
            <p className="text-sm text-muted-foreground">
              {tracks.length} track{tracks.length !== 1 ? 's' : ''} • {formatDuration(totalDuration)}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {onPlaylistClear && tracks.length > 0 && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onPlaylistClear}
                className="text-destructive hover:text-destructive"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="md:hidden"
            >
              <svg
                className={`w-4 h-4 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className={`p-0 ${isExpanded ? 'block' : 'hidden'} md:block`}>
        <ScrollArea className="h-96">
          {tracks.map((track, index) => (
            <div
              key={`${track.url}-${index}`}
              className={`flex items-center p-3 hover:bg-accent hover:text-accent-foreground cursor-pointer border-l-4 transition-colors ${
                index === currentTrackIndex
                  ? 'bg-accent text-accent-foreground border-primary'
                  : 'border-transparent'
              }`}
              onClick={() => onTrackSelect(index)}
            >
              {/* Track Number / Playing Indicator */}
              <div className="w-8 text-center flex-shrink-0">
                {index === currentTrackIndex ? (
                  <div className="text-primary">
                    <svg className="w-4 h-4 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 5v10l8-5-8-5z"/>
                    </svg>
                  </div>
                ) : (
                  <span className="text-sm text-muted-foreground">{index + 1}</span>
                )}
              </div>

              {/* Album Art */}
              <div className="w-10 h-10 bg-muted rounded mr-3 flex-shrink-0">
                {track.picture ? (
                  <img
                    src={track.picture}
                    alt={track.name}
                    className="w-full h-full object-cover rounded"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                    ♪
                  </div>
                )}
              </div>

              {/* Track Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div className="min-w-0 flex-1">
                    <h4 className={`text-sm font-medium truncate ${
                      index === currentTrackIndex ? 'text-primary' : 'text-card-foreground'
                    }`}>
                      {track.name}
                    </h4>
                    <p className="text-xs text-muted-foreground truncate">
                      {track.artist || 'Unknown Artist'}
                      {track.album && ` • ${track.album}`}
                    </p>
                  </div>
                  
                  {/* Duration */}
                  <div className="flex items-center space-x-2 ml-2">
                    <span className="text-xs text-muted-foreground">
                      {formatDuration(track.duration)}
                    </span>

                    {/* Remove button */}
                    {onTrackRemove && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onTrackRemove(index);
                        }}
                        className="text-muted-foreground hover:text-destructive p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                        title="Remove from playlist"
                      >
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </ScrollArea>
      </CardContent>

      {/* Footer with playlist actions */}
      <div className="p-3 border-t border-border bg-muted/50">
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>
            Track {currentTrackIndex + 1} of {tracks.length}
          </span>
          <div className="flex items-center space-x-4">
            <span>Total: {formatDuration(totalDuration)}</span>
          </div>
        </div>
      </div>
    </Card>
  );
}
